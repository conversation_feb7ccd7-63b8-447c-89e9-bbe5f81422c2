<?php
namespace App\Modules\Sales\Models;

use PDO;
use Exception;

/**
 * Customer Model - نموذج العملاء
 */
class Customer
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Table name
     */
    protected $table = 'concerned_entities';

    /**
     * Section ID for customers
     */
    protected $section_id;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
        
        // الحصول على section_id للعملاء
        $stmt = $this->db->prepare("SELECT id FROM sections WHERE code = 'customers'");
        $stmt->execute();
        $this->section_id = $stmt->fetchColumn();

        if (!$this->section_id) {
            // محاولة إنشاء section العملاء تلقائياً
            try {
                $insertStmt = $this->db->prepare("INSERT IGNORE INTO sections (code, description) VALUES ('customers', 'العملاء الذين يتم البيع لهم')");
                $insertStmt->execute();

                // محاولة الحصول على الـ ID مرة أخرى
                $stmt->execute();
                $this->section_id = $stmt->fetchColumn();

                if (!$this->section_id) {
                    throw new Exception("Failed to create or find 'customers' section");
                }
            } catch (Exception $e) {
                throw new Exception("Section 'customers' not found and could not be created: " . $e->getMessage());
            }
        }
    }

    /**
     * الحصول على جميع العملاء للشركة
     */
    public function getByCompany($company_id, $filters = [])
    {
        $sql = "SELECT s.*, g.name_ar as group_name 
                FROM {$this->table} s
                LEFT JOIN entity_groups g ON s.group_id = g.id
                WHERE s.company_id = ? AND s.section_id = ?";
        $params = [$company_id, $this->section_id];

        // تطبيق الفلاتر
        if (isset($filters['status']) && $filters['status'] !== '') {
            $sql .= " AND s.G_status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['group_id'])) {
            $sql .= " AND s.group_id = ?";
            $params[] = $filters['group_id'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (s.G_name_ar LIKE ? OR s.G_name_en LIKE ? OR s.C_email LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }

        // ترتيب النتائج
        if (isset($filters['order_by'])) {
            $sql .= " ORDER BY " . $filters['order_by'];
        } else {
            $sql .= " ORDER BY s.G_name_ar";
        }

        // إضافة pagination
        if (isset($filters['limit']) && isset($filters['offset'])) {
            $limit = (int)$filters['limit'];
            $offset = (int)$filters['offset'];
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        } elseif (isset($filters['limit'])) {
            $limit = (int)$filters['limit'];
            $sql .= " LIMIT {$limit}";
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على عميل بالرقم الداخلي
     */
    public function getByNumber($entity_number, $company_id)
    {
        $sql = "SELECT s.*, g.name_ar as group_name
                FROM {$this->table} s
                LEFT JOIN entity_groups g ON s.group_id = g.id
                WHERE s.entity_number = ? AND s.company_id = ? AND s.section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$entity_number, $company_id, $this->section_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * البحث عن عميل بالاسم والشركة
     */
    public function findByNameAndCompany($name, $company_id)
    {
        $sql = "SELECT * FROM {$this->table}
                WHERE G_name_ar = ? AND company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$name, $company_id, $this->section_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء عميل جديد
     */
    public function create($data)
    {
        // الحصول على الرقم التالي
        $nextNumber = $this->getNextNumber($data['company_id']);

        $sql = "INSERT INTO {$this->table} (
                    company_id, section_id, entity_number, group_id,
                    G_name_ar, G_name_en, G_phone, G_mobile, G_website, G_notes, G_status,
                    C_email, C_tax_number, C_commercial_register, C_credit_limit,
                    C_payment_terms, C_discount_rate, C_customer_type, C_price_list,
                    C_sales_rep_id, C_territory, C_industry, C_source, C_rating,
                    created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $data['company_id'],
            $this->section_id,
            $nextNumber,
            $data['group_id'] ?: null,
            $data['G_name_ar'],
            $data['G_name_en'] ?: null,
            $data['G_phone'] ?: null,
            $data['G_mobile'] ?: null,
            $data['G_website'] ?: null,
            $data['G_notes'] ?: null,
            $data['G_status'] ?: 'active',
            $data['C_email'] ?: null,
            $data['C_tax_number'] ?: null,
            $data['C_commercial_register'] ?: null,
            $data['C_credit_limit'] ?: 0,
            $data['C_payment_terms'] ?: 0,
            $data['C_discount_rate'] ?: 0,
            $data['C_customer_type'] ?: 'individual',
            $data['C_price_list'] ?: null,
            $data['C_sales_rep_id'] ?: null,
            $data['C_territory'] ?: null,
            $data['C_industry'] ?: null,
            $data['C_source'] ?: null,
            $data['C_rating'] ?: 'C',
            $data['created_by']
        ]);

        return $result ? $nextNumber : false;
    }

    /**
     * تحديث عميل
     */
    public function update($entity_number, $data, $company_id)
    {
        $sql = "UPDATE {$this->table} SET 
                    group_id = ?, G_name_ar = ?, G_name_en = ?, G_phone = ?, G_mobile = ?,
                    G_website = ?, G_notes = ?, G_status = ?,
                    C_email = ?, C_tax_number = ?, C_commercial_register = ?, C_credit_limit = ?,
                    C_payment_terms = ?, C_discount_rate = ?, C_customer_type = ?, C_price_list = ?,
                    C_sales_rep_id = ?, C_territory = ?, C_industry = ?, C_source = ?, C_rating = ?,
                    updated_by = ?, updated_at = NOW()
                WHERE entity_number = ? AND company_id = ? AND section_id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['group_id'] ?: null,
            $data['G_name_ar'],
            $data['G_name_en'] ?: null,
            $data['G_phone'] ?: null,
            $data['G_mobile'] ?: null,
            $data['G_website'] ?: null,
            $data['G_notes'] ?: null,
            $data['G_status'] ?: 'active',
            $data['C_email'] ?: null,
            $data['C_tax_number'] ?: null,
            $data['C_commercial_register'] ?: null,
            $data['C_credit_limit'] ?: 0,
            $data['C_payment_terms'] ?: 0,
            $data['C_discount_rate'] ?: 0,
            $data['C_customer_type'] ?: 'individual',
            $data['C_price_list'] ?: null,
            $data['C_sales_rep_id'] ?: null,
            $data['C_territory'] ?: null,
            $data['C_industry'] ?: null,
            $data['C_source'] ?: null,
            $data['C_rating'] ?: 'C',
            $data['updated_by'],
            $entity_number,
            $company_id,
            $this->section_id
        ]);
    }

    /**
     * حذف عميل
     */
    public function delete($entity_number, $company_id)
    {
        $sql = "DELETE FROM {$this->table} 
                WHERE entity_number = ? AND company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$entity_number, $company_id, $this->section_id]);
    }

    /**
     * الحصول على الرقم التالي للعميل
     */
    protected function getNextNumber($company_id)
    {
        $sql = "SELECT MAX(entity_number) FROM {$this->table} 
                WHERE company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $lastNumber = $stmt->fetchColumn();
        
        return ($lastNumber ?: 0) + 1;
    }

    /**
     * الحصول على عدد العملاء مع الفلاتر
     */
    public function getCountByCompany($company_id, $filters = [])
    {
        $sql = "SELECT COUNT(*) FROM {$this->table} s
                LEFT JOIN entity_groups g ON s.group_id = g.id
                WHERE s.company_id = ? AND s.section_id = ?";

        $params = [$company_id, $this->section_id];

        // تطبيق الفلاتر
        if (isset($filters['status']) && $filters['status'] !== '') {
            $sql .= " AND s.G_status = ?";
            $params[] = $filters['status'];
        }

        if (isset($filters['group_id']) && $filters['group_id'] !== '') {
            $sql .= " AND s.group_id = ?";
            $params[] = $filters['group_id'];
        }

        if (isset($filters['search']) && $filters['search'] !== '') {
            $sql .= " AND (s.G_name_ar LIKE ? OR s.G_name_en LIKE ? OR s.C_email LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return (int)$stmt->fetchColumn();
    }

    /**
     * الحصول على إحصائيات العملاء
     */
    public function getStats($company_id)
    {
        $stats = [];

        // إجمالي العملاء
        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $stats['total_customers'] = $stmt->fetchColumn();

        // العملاء النشطين
        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE company_id = ? AND section_id = ? AND G_status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $stats['active_customers'] = $stmt->fetchColumn();

        // العملاء حسب النوع
        $sql = "SELECT C_customer_type, COUNT(*) as count FROM {$this->table}
                WHERE company_id = ? AND section_id = ?
                GROUP BY C_customer_type";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $customerTypes = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        $stats['individual_customers'] = $customerTypes['individual'] ?? 0;
        $stats['company_customers'] = $customerTypes['company'] ?? 0;

        return $stats;
    }

    /**
     * الحصول على العملاء للاختيار
     */
    public function getForSelect($company_id)
    {
        $sql = "SELECT entity_number, G_name_ar FROM {$this->table}
                WHERE company_id = ? AND section_id = ? AND G_status = 'active'
                ORDER BY G_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على عميل مع العناوين والحسابات البنكية
     */
    public function getWithRelations($entity_number, $company_id)
    {
        // جلب بيانات العميل الأساسية
        $customer = $this->getByNumber($entity_number, $company_id);

        if ($customer) {
            // جلب العناوين
            $customer['addresses'] = $this->getAddresses($customer['id'], $company_id);

            // جلب الحسابات البنكية
            $customer['bank_accounts'] = $this->getBankAccounts($customer['id'], $company_id);
        }

        return $customer;
    }

    /**
     * الحصول على عناوين العميل
     */
    protected function getAddresses($entity_id, $company_id)
    {
        $sql = "SELECT * FROM entity_addresses
                WHERE entity_id = ? AND company_id = ?
                ORDER BY is_default DESC, id ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$entity_id, $company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على الحسابات البنكية للعميل
     */
    protected function getBankAccounts($entity_id, $company_id)
    {
        $sql = "SELECT * FROM entity_bank_accounts
                WHERE entity_id = ? AND company_id = ?
                ORDER BY is_default DESC, id ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$entity_id, $company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * بدء معاملة قاعدة البيانات
     */
    public function beginTransaction()
    {
        return $this->db->beginTransaction();
    }

    /**
     * تأكيد معاملة قاعدة البيانات
     */
    public function commit()
    {
        return $this->db->commit();
    }

    /**
     * إلغاء معاملة قاعدة البيانات
     */
    public function rollback()
    {
        return $this->db->rollback();
    }

    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getDb()
    {
        return $this->db;
    }
}
