<?php
namespace App\Modules\Purchases\Controllers;

use App\Modules\Purchases\Models\Supplier;
use App\Modules\Purchases\Models\SupplierGroup;
use Exception;
use PDO;

/**
 * SupplierController - متحكم الموردين
 */
class SupplierController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Supplier model
     */
    protected $supplierModel;

    /**
     * SupplierGroup model
     */
    protected $supplierGroupModel;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->supplierModel = new Supplier();
        $this->supplierGroupModel = new SupplierGroup();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash('supplier_error', 'يجب تحديد شركة حالية للوصول إلى الموردين', 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * عرض قائمة الموردين
     */
    public function index()
    {
        // استخدام الدالة الموحدة لمعالجة index
        $data = handle_datatable_index([
            'filter_name' => 'suppliers',
            'default_filters' => [
                'status' => '',
                'group_id' => ''
            ],
            'filter_fields' => ['search', 'status', 'group_id'],
            'model' => $this->supplierModel,
            'title' => 'الموردين',
            'data_key' => 'suppliers',
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'الموردين', 'active' => true]
            ],
            'additional_data' => [
                'supplierGroups' => $this->supplierGroupModel->getForSelect(current_user()['current_company_id'])
            ]
        ]);

        view('Purchases::suppliers/index', $data);
    }

    /**
     * تطبيق فلاتر الموردين (POST form submission)
     */
    public function applyFilters()
    {
        handle_apply_filters('suppliers', ['status', 'group_id'], base_url('purchases/suppliers'));
    }

    /**
     * مسح فلاتر الموردين
     */
    public function clearFilters()
    {
        handle_clear_filters('suppliers', base_url('purchases/suppliers'));
    }

    /**
     * عرض إحصائيات الموردين
     */
    public function stats()
    {
        $company_id = current_user()['current_company_id'];

        // الحصول على الإحصائيات
        $stats = $this->supplierModel->getStats($company_id);

        $data = [
            'title' => 'إحصائيات الموردين',
            'stats' => $stats,
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'الموردين', 'url' => base_url('purchases/suppliers')],
                ['title' => 'الإحصائيات', 'active' => true]
            ]
        ];

        view('Purchases::suppliers/stats', $data);
    }
public function mixed()
    {
        // استخدام الدالة الموحدة لمعالجة index (للجدول)
        $data = handle_datatable_index([
            'filter_name' => 'suppliers',
            'default_filters' => [
                'status' => '',
                'group_id' => ''
            ],
            'filter_fields' => ['search', 'status', 'group_id'],
            'model' => $this->supplierModel,
            'title' => 'الموردين - عرض مختلط',
            'data_key' => 'suppliers',
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'الموردين - عرض مختلط', 'active' => true]
            ],
            'additional_data' => [
                'supplierGroups' => $this->supplierGroupModel->getForSelect(current_user()['current_company_id'])
            ]
        ]);

        // إضافة الإحصائيات
        $company_id = current_user()['current_company_id'];
        $data['stats'] = $this->supplierModel->getStats($company_id);

        view('Purchases::suppliers/mixed', $data);
    }
    /**
     * عرض صفحة إضافة مورد جديد
     */
    public function create()
    {
        $company_id = current_user()['current_company_id'];

        // الحصول على مجموعات الموردين
        $supplierGroups = $this->supplierGroupModel->getForSelect($company_id);

        // الحصول على المجموعة الافتراضية
        $defaultGroup = $this->supplierGroupModel->getDefaultGroup($company_id);

        $data = [
            'title' => 'إضافة مورد جديد',
            'supplierGroups' => $supplierGroups,
            'defaultGroup' => $defaultGroup,
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'الموردين', 'url' => base_url('purchases/suppliers')],
                ['title' => 'إضافة مورد', 'active' => true]
            ]
        ];

        view('Purchases::suppliers/create', $data);
    }

    /**
     * حفظ مورد جديد
     */
    public function store()
    {
        try {
            // إعداد معلومات المستخدم والشركة أولاً
            $company_id = current_user()['current_company_id'];
            $user_id = current_user()['UserID'];

            // بدء المعاملة
            $this->supplierModel->beginTransaction();

            // التحقق من صحة البيانات (بعد بدء المعاملة)
            $validatedData = $this->validateSupplierData($_POST);
            $validatedData['company_id'] = $company_id;
            $validatedData['created_by'] = $user_id;

            // إنشاء المورد
            $supplierNumber = $this->supplierModel->create($validatedData);

            if ($supplierNumber) {
                // الحصول على entity_id للمورد الجديد
                $supplier = $this->supplierModel->getByNumber($supplierNumber, $company_id);
                $entity_id = $supplier['id'];

                // حفظ العناوين
                if (!empty($_POST['addresses'])) {
                    $this->saveAddresses($entity_id, $company_id, $_POST['addresses'], $_POST['default_address'] ?? 0, $user_id);
                }

                // حفظ الحسابات البنكية
                if (!empty($_POST['bank_accounts'])) {
                    $this->saveBankAccounts($entity_id, $company_id, $_POST['bank_accounts'], $_POST['default_bank_account'] ?? 0, $user_id);
                }

                // تأكيد المعاملة
                $this->supplierModel->commit();

                flash('success', 'تم إنشاء المورد بنجاح مع جميع البيانات');
                redirect(base_url('purchases/suppliers'));
            } else {
                $this->supplierModel->rollback();
                flash('error', 'حدث خطأ أثناء إنشاء المورد');
                redirect(base_url('purchases/suppliers/create'));
            }

        } catch (Exception $e) {
            $this->supplierModel->rollback();
            flash('error', $e->getMessage());
            redirect(base_url('purchases/suppliers/create'));
        }
    }

    /**
     * عرض تفاصيل مورد
     */
    public function show()
    {
        $entity_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplier = $this->supplierModel->getWithRelations($entity_number, $company_id);

        if (!$supplier) {
            flash('error', 'المورد غير موجود');
            redirect(base_url('purchases/suppliers'));
        }

        // الحصول على مجموعات الموردين
        $supplierGroups = $this->supplierGroupModel->getForSelect($company_id);

        $data = [
            'title' => 'عرض المورد - ' . $supplier['G_name_ar'],
            'supplier' => $supplier,
            'supplierGroups' => $supplierGroups,
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'الموردين', 'url' => base_url('purchases/suppliers')],
                ['title' => 'عرض المورد', 'active' => true]
            ]
        ];

        view('Purchases::suppliers/show', $data);
    }

    /**
     * عرض صفحة تعديل مورد
     */
    public function edit()
    {
        $entity_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplier = $this->supplierModel->getWithRelations($entity_number, $company_id);

        if (!$supplier) {
            flash('error', 'المورد غير موجود');
            redirect(base_url('purchases/suppliers'));
        }

        // الحصول على مجموعات الموردين
        $supplierGroups = $this->supplierGroupModel->getForSelect($company_id);

        $data = [
            'title' => 'تعديل المورد - ' . $supplier['G_name_ar'],
            'supplier' => $supplier,
            'supplierGroups' => $supplierGroups,
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'الموردين', 'url' => base_url('purchases/suppliers')],
                ['title' => 'تعديل المورد', 'active' => true]
            ]
        ];

        view('Purchases::suppliers/edit', $data);
    }



    /**
     * تحديث مورد
     */
    public function update()
    {
        try {
            $entity_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المورد
            $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);
            if (!$supplier) {
                flash('error', 'المورد غير موجود');
                redirect(base_url('purchases/suppliers'));
            }

            // التحقق من صحة البيانات
            $validatedData = $this->validateSupplierData($_POST, $entity_number);

            // فحص التغييرات قبل التحديث (تمرير $_POST للفحص الشامل)
            if (!$this->hasDataChanged($supplier, $validatedData, $_POST)) {
                flash('info', 'لا توجد تغييرات لحفظها');
                redirect(base_url('purchases/suppliers/' . $entity_number . '/edit'));
                return;
            }

            $validatedData['updated_by'] = current_user()['UserID'];

            // بدء المعاملة
            $db = $this->supplierModel->getDb();
            $db->beginTransaction();

            try {
                // تحديث البيانات الأساسية للمورد
                $result = $this->supplierModel->update($entity_number, $validatedData, $company_id);

                if (!$result) {
                    throw new Exception('فشل في تحديث البيانات الأساسية للمورد');
                }

                // الحصول على entity_id
                $entity_id = $supplier['id'];
                $user_id = current_user()['UserID'];

                // تحديث العناوين
                $this->updateAddresses($entity_id, $company_id, $_POST, $user_id);

                // تحديث الحسابات البنكية
                $this->updateBankAccounts($entity_id, $company_id, $_POST, $user_id);

                // تأكيد المعاملة
                $db->commit();

                flash('success', 'تم تحديث المورد بنجاح - رقم المورد: ' . $entity_number);
                redirect(base_url('purchases/suppliers'));

            } catch (Exception $e) {
                // إلغاء المعاملة
                $db->rollBack();
                throw $e;
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            redirect(base_url('purchases/suppliers/' . $this->params['id'] . '/edit'));
        }
    }

    /**
     * حذف مورد
     */
    public function delete()
    {
        try {
            $entity_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المورد
            $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);
            if (!$supplier) {
                flash('error', 'المورد غير موجود');
                redirect(base_url('purchases/suppliers'));
            }

            // حذف المورد
            $result = $this->supplierModel->delete($entity_number, $company_id);

            if ($result) {
                flash('success', 'تم حذف المورد بنجاح');
            } else {
                flash('error', 'حدث خطأ أثناء حذف المورد');
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
        }

        redirect(base_url('purchases/suppliers'));
    }

    /**
     * فحص ما إذا كانت البيانات تغيرت
     */
    private function hasDataChanged($originalData, $newData)
    {
        // الحقول التي نريد مقارنتها
        $fieldsToCompare = [
            'group_id', 'G_name_ar', 'G_name_en', 'G_phone', 'G_mobile',
            'G_website', 'G_notes', 'G_status', 'S_company_name', 'S_contact_person',
            'S_email', 'S_tax_number', 'S_commercial_register', 'S_payment_terms',
            'S_credit_limit', 'S_discount_rate', 'S_delivery_time', 'S_minimum_order',
            'S_currency', 'S_rating', 'S_license_number', 'S_license_expiry',
            'S_establishment_date', 'S_legal_form', 'S_internal_notes', 'S_special_instructions'
        ];

        foreach ($fieldsToCompare as $field) {
            $originalValue = $originalData[$field] ?? '';
            $newValue = $newData[$field] ?? '';

            // تنظيف القيم للمقارنة
            $cleanOriginal = trim((string)$originalValue);
            $cleanNew = trim((string)$newValue);

            // معالجة القيم الفارغة والـ null
            if ($cleanOriginal === '' && $cleanNew === '') {
                continue;
            }

            if ($cleanOriginal === '' && $cleanNew === '0') {
                continue;
            }

            if ($cleanOriginal === '0' && $cleanNew === '') {
                continue;
            }

            // مقارنة القيم
            if ($cleanOriginal !== $cleanNew) {
                return true; // يوجد تغيير
            }
        }

        // فحص العناوين والحسابات البنكية إذا تم تمرير POST data
        if ($postData) {
            // فحص العناوين
            if ($this->hasAddressesChanged($originalData, $postData)) {
                return true;
            }

            // فحص الحسابات البنكية
            if ($this->hasBankAccountsChanged($originalData, $postData)) {
                return true;
            }
        }

        return false; // لا توجد تغييرات
    }

    /**
     * فحص تغييرات العناوين
     */
    private function hasAddressesChanged($originalData, $postData)
    {
        // الحصول على العناوين الحالية من قاعدة البيانات
        $currentAddresses = $this->getCurrentAddresses($originalData['id'] ?? 0);

        // الحصول على العناوين الجديدة من POST
        $newAddresses = $postData['addresses'] ?? [];

        // مقارنة عدد العناوين
        if (count($currentAddresses) !== count($newAddresses)) {
            return true;
        }

        return false; // مؤقت - سنضيف المقارنة التفصيلية لاحقاً
    }

    /**
     * فحص تغييرات الحسابات البنكية
     */
    private function hasBankAccountsChanged($originalData, $postData)
    {
        // الحصول على الحسابات البنكية الحالية من قاعدة البيانات
        $currentBankAccounts = $this->getCurrentBankAccounts($originalData['id'] ?? 0);

        // الحصول على الحسابات البنكية الجديدة من POST
        $newBankAccounts = $postData['bank_accounts'] ?? [];

        // مقارنة عدد الحسابات
        if (count($currentBankAccounts) !== count($newBankAccounts)) {
            return true;
        }

        return false; // مؤقت - سنضيف المقارنة التفصيلية لاحقاً
    }

    /**
     * الحصول على العناوين الحالية
     */
    private function getCurrentAddresses($entityId)
    {
        if (!$entityId) return [];

        $sql = "SELECT address_type, address_label, address_line1, city, state_province,
                       postal_code, country, phone, is_default
                FROM entity_addresses
                WHERE entity_id = ?
                ORDER BY id";

        $db = $this->supplierModel->getDb();
        $stmt = $db->prepare($sql);
        $stmt->execute([$entityId]);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على الحسابات البنكية الحالية
     */
    private function getCurrentBankAccounts($entityId)
    {
        if (!$entityId) return [];

        $sql = "SELECT bank_name, account_number, account_name, iban, currency, is_default
                FROM entity_bank_accounts
                WHERE entity_id = ?
                ORDER BY id";

        $db = $this->supplierModel->getDb();
        $stmt = $db->prepare($sql);
        $stmt->execute([$entityId]);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * التحقق من صحة بيانات المورد
     */
    private function validateSupplierData($data, $excludeEntityNumber = null)
    {
        $errors = [];

        // التحقق من الاسم العربي
        if (empty($data['G_name_ar'])) {
            $errors[] = 'اسم المورد بالعربية مطلوب';
        }

        // التحقق من تكرار الاسم في الشركة الحالية
        if (!empty($data['G_name_ar'])) {
            $company_id = current_user()['current_company_id'];
            $existing_supplier = $this->supplierModel->findByNameAndCompany($data['G_name_ar'], $company_id);

            // إذا وُجد مورد بنفس الاسم وليس هو نفس المورد الحالي (في حالة التعديل)
            if ($existing_supplier && (!$excludeEntityNumber || $existing_supplier['entity_number'] != $excludeEntityNumber)) {
                $errors[] = 'اسم المورد موجود بالفعل في الشركة الحالية';
            }
        }

        // التحقق من البريد الإلكتروني
        if (!empty($data['S_email']) && !filter_var($data['S_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }

        // التحقق من الموقع الإلكتروني
        if (!empty($data['G_website']) && !filter_var($data['G_website'], FILTER_VALIDATE_URL)) {
            $errors[] = 'الموقع الإلكتروني غير صحيح';
        }

        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }

        // إعداد البيانات
        return [
            'group_id' => !empty($data['group_id']) ? (int)$data['group_id'] : null,
            'G_name_ar' => trim($data['G_name_ar']),
            'G_name_en' => trim($data['G_name_en'] ?? ''),
            'G_phone' => trim($data['G_phone'] ?? ''),
            'G_mobile' => trim($data['G_mobile'] ?? ''),
            'G_website' => trim($data['G_website'] ?? ''),
            'G_notes' => trim($data['G_notes'] ?? ''),
            'G_status' => $data['G_status'] ?? 'active',
            'S_company_name' => trim($data['S_company_name'] ?? ''),
            'S_contact_person' => trim($data['S_contact_person'] ?? ''),
            'S_email' => trim($data['S_email'] ?? ''),
            'S_tax_number' => trim($data['S_tax_number'] ?? ''),
            'S_commercial_register' => trim($data['S_commercial_register'] ?? ''),
            'S_payment_terms' => !empty($data['S_payment_terms']) ? (int)$data['S_payment_terms'] : 30,
            'S_credit_limit' => !empty($data['S_credit_limit']) ? (float)$data['S_credit_limit'] : 0,
            'S_discount_rate' => !empty($data['S_discount_rate']) ? (float)$data['S_discount_rate'] : 0,
            'S_delivery_time' => !empty($data['S_delivery_time']) ? (int)$data['S_delivery_time'] : null,
            'S_minimum_order' => !empty($data['S_minimum_order']) ? (float)$data['S_minimum_order'] : null,
            'S_currency' => $data['S_currency'] ?? 'SAR',
            'S_rating' => $data['S_rating'] ?? 'C',
            // الحقول الجديدة - المعلومات القانونية
            'S_license_number' => trim($data['S_license_number'] ?? ''),
            'S_license_expiry' => !empty($data['S_license_expiry']) ? $data['S_license_expiry'] : null,
            'S_establishment_date' => !empty($data['S_establishment_date']) ? $data['S_establishment_date'] : null,
            'S_legal_form' => $data['S_legal_form'] ?? null,
            // الحقول الجديدة - الملاحظات الإضافية
            'S_internal_notes' => trim($data['S_internal_notes'] ?? ''),
            'S_special_instructions' => trim($data['S_special_instructions'] ?? '')
        ];
    }

    /**
     * تحديث عناوين المورد
     */
    private function updateAddresses($entity_id, $company_id, $postData, $user_id)
    {
        // حذف العناوين الحالية
        $deleteSql = "DELETE FROM entity_addresses WHERE entity_id = ? AND company_id = ?";
        $db = $this->supplierModel->getDb();
        $deleteStmt = $db->prepare($deleteSql);
        $deleteStmt->execute([$entity_id, $company_id]);

        // إضافة العناوين الجديدة
        if (!empty($postData['addresses'])) {
            $addresses = $postData['addresses'];
            $default_index = $postData['default_address'] ?? 0;
            $this->saveAddresses($entity_id, $company_id, $addresses, $default_index, $user_id);
        }
    }

    /**
     * تحديث الحسابات البنكية للمورد
     */
    private function updateBankAccounts($entity_id, $company_id, $postData, $user_id)
    {
        // حذف الحسابات البنكية الحالية
        $deleteSql = "DELETE FROM entity_bank_accounts WHERE entity_id = ? AND company_id = ?";
        $db = $this->supplierModel->getDb();
        $deleteStmt = $db->prepare($deleteSql);
        $deleteStmt->execute([$entity_id, $company_id]);

        // إضافة الحسابات البنكية الجديدة
        if (!empty($postData['bank_accounts'])) {
            $bank_accounts = $postData['bank_accounts'];
            $default_index = $postData['default_bank_account'] ?? 0;
            $this->saveBankAccounts($entity_id, $company_id, $bank_accounts, $default_index, $user_id);
        }
    }

    /**
     * حفظ عناوين المورد
     */
    private function saveAddresses($entity_id, $company_id, $addresses, $default_index, $user_id)
    {
        $sql = "INSERT INTO entity_addresses (
                    entity_id, company_id, address_type, address_label, address_line1,
                    city, state_province, postal_code, country, phone, is_default,
                    created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $db = $this->supplierModel->getDb();
        $stmt = $db->prepare($sql);

        foreach ($addresses as $index => $address) {
            if (empty($address['address_type']) || empty($address['address_label']) || empty($address['address_line1'])) {
                continue; // تخطي العناوين غير المكتملة
            }

            $is_default = ($index == $default_index) ? 1 : 0;

            $stmt->execute([
                $entity_id,
                $company_id,
                $address['address_type'],
                $address['address_label'],
                $address['address_line1'],
                $address['city'],
                $address['state_province'] ?? null,
                $address['postal_code'] ?? null,
                $address['country'] ?? 'Saudi Arabia',
                $address['phone'] ?? null,
                $is_default,
                $user_id
            ]);
        }
    }

    /**
     * حفظ الحسابات البنكية للمورد
     */
    private function saveBankAccounts($entity_id, $company_id, $bank_accounts, $default_index, $user_id)
    {
        $sql = "INSERT INTO entity_bank_accounts (
                    entity_id, company_id, bank_name, account_number, account_name,
                    iban, currency, is_default, created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $db = $this->supplierModel->getDb();
        $stmt = $db->prepare($sql);

        foreach ($bank_accounts as $index => $account) {
            if (empty($account['bank_name']) || empty($account['account_number']) || empty($account['account_name'])) {
                continue; // تخطي الحسابات غير المكتملة
            }

            $is_default = ($index == $default_index) ? 1 : 0;

            $stmt->execute([
                $entity_id,
                $company_id,
                $account['bank_name'],
                $account['account_number'],
                $account['account_name'],
                $account['iban'] ?? null,
                $account['currency'] ?? 'SAR',
                $is_default,
                $user_id
            ]);
        }
    }
}
