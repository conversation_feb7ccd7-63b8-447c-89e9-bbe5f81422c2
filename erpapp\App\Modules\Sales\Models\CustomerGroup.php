<?php
namespace App\Modules\Sales\Models;

use PDO;
use Exception;

/**
 * CustomerGroup Model - نموذج مجموعات العملاء
 */
class CustomerGroup
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Table name
     */
    protected $table = 'entity_groups';

    /**
     * Section ID for customers
     */
    protected $section_id;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
        
        // الحصول على section_id للعملاء
        $stmt = $this->db->prepare("SELECT id FROM sections WHERE code = 'customers'");
        $stmt->execute();
        $this->section_id = $stmt->fetchColumn();
        
        if (!$this->section_id) {
            throw new Exception("Section 'customers' not found");
        }
    }

    /**
     * الحصول على جميع مجموعات العملاء للشركة
     */
    public function getByCompany($company_id, $filters = [])
    {
        $sql = "SELECT * FROM {$this->table}
                WHERE company_id = ? AND section_id = ?";
        $params = [$company_id, $this->section_id];

        // تطبيق الفلاتر
        if (!empty($filters['search'])) {
            $sql .= " AND (name_ar LIKE ? OR name_en LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
        }

        if (isset($filters['status']) && $filters['status'] !== '') {
            $sql .= " AND status = ?";
            $params[] = $filters['status'];
        }

        // ترتيب النتائج
        $sql .= " ORDER BY name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على مجموعة عملاء بالمعرف
     */
    public function getById($id, $company_id)
    {
        $sql = "SELECT * FROM {$this->table}
                WHERE id = ? AND company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id, $company_id, $this->section_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء مجموعة عملاء جديدة
     */
    public function create($data)
    {
        $sql = "INSERT INTO {$this->table} (
                    company_id, section_id, name_ar, name_en, description, status,
                    created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $data['company_id'],
            $this->section_id,
            $data['name_ar'],
            $data['name_en'] ?: null,
            $data['description'] ?: null,
            $data['status'] ?: 'active',
            $data['created_by']
        ]);

        return $result ? $this->db->lastInsertId() : false;
    }

    /**
     * تحديث مجموعة عملاء
     */
    public function update($id, $data, $company_id)
    {
        $sql = "UPDATE {$this->table} SET 
                    name_ar = ?, name_en = ?, description = ?, status = ?,
                    updated_by = ?, updated_at = NOW()
                WHERE id = ? AND company_id = ? AND section_id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['name_ar'],
            $data['name_en'] ?: null,
            $data['description'] ?: null,
            $data['status'] ?: 'active',
            $data['updated_by'],
            $id,
            $company_id,
            $this->section_id
        ]);
    }

    /**
     * حذف مجموعة عملاء
     */
    public function delete($id, $company_id)
    {
        // التحقق من عدم وجود عملاء في هذه المجموعة
        $checkSql = "SELECT COUNT(*) FROM concerned_entities 
                     WHERE group_id = ? AND company_id = ? AND section_id = ?";
        $checkStmt = $this->db->prepare($checkSql);
        $checkStmt->execute([$id, $company_id, $this->section_id]);
        
        if ($checkStmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف المجموعة لوجود عملاء بها');
        }

        $sql = "DELETE FROM {$this->table} 
                WHERE id = ? AND company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$id, $company_id, $this->section_id]);
    }

    /**
     * الحصول على مجموعات العملاء للاختيار
     */
    public function getForSelect($company_id)
    {
        $sql = "SELECT id, name_ar FROM {$this->table}
                WHERE company_id = ? AND section_id = ? AND status = 'active'
                ORDER BY name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على المجموعة الافتراضية
     */
    public function getDefaultGroup($company_id)
    {
        $sql = "SELECT * FROM {$this->table}
                WHERE company_id = ? AND section_id = ? AND status = 'active'
                ORDER BY id ASC
                LIMIT 1";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * البحث عن مجموعة بالاسم
     */
    public function findByName($name, $company_id)
    {
        $sql = "SELECT * FROM {$this->table}
                WHERE name_ar = ? AND company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$name, $company_id, $this->section_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على عدد المجموعات
     */
    public function getCount($company_id, $filters = [])
    {
        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE company_id = ? AND section_id = ?";
        $params = [$company_id, $this->section_id];

        // تطبيق الفلاتر
        if (!empty($filters['search'])) {
            $sql .= " AND (name_ar LIKE ? OR name_en LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
        }

        if (isset($filters['status']) && $filters['status'] !== '') {
            $sql .= " AND status = ?";
            $params[] = $filters['status'];
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return (int)$stmt->fetchColumn();
    }

    /**
     * الحصول على إحصائيات المجموعات
     */
    public function getStats($company_id)
    {
        $stats = [];

        // إجمالي المجموعات
        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $stats['total_groups'] = $stmt->fetchColumn();

        // المجموعات النشطة
        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE company_id = ? AND section_id = ? AND status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $stats['active_groups'] = $stmt->fetchColumn();

        // عدد العملاء في كل مجموعة
        $sql = "SELECT g.name_ar, COUNT(c.id) as customer_count
                FROM {$this->table} g
                LEFT JOIN concerned_entities c ON g.id = c.group_id AND c.section_id = ?
                WHERE g.company_id = ? AND g.section_id = ?
                GROUP BY g.id, g.name_ar
                ORDER BY customer_count DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$this->section_id, $company_id, $this->section_id]);
        $stats['groups_with_counts'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $stats;
    }

    /**
     * التحقق من صحة البيانات
     */
    public function validateData($data, $excludeId = null)
    {
        $errors = [];

        // التحقق من الاسم العربي
        if (empty($data['name_ar'])) {
            $errors[] = 'اسم المجموعة بالعربية مطلوب';
        } else {
            // التحقق من تكرار الاسم
            $existing = $this->findByName($data['name_ar'], $data['company_id']);
            if ($existing && (!$excludeId || $existing['id'] != $excludeId)) {
                $errors[] = 'اسم المجموعة موجود بالفعل';
            }
        }

        // التحقق من الحالة
        if (!empty($data['status']) && !in_array($data['status'], ['active', 'inactive'])) {
            $errors[] = 'حالة المجموعة غير صحيحة';
        }

        return $errors;
    }

    /**
     * تنظيف البيانات
     */
    public function sanitizeData($data)
    {
        return [
            'company_id' => (int)$data['company_id'],
            'name_ar' => trim($data['name_ar']),
            'name_en' => trim($data['name_en'] ?? ''),
            'description' => trim($data['description'] ?? ''),
            'status' => $data['status'] ?? 'active',
            'created_by' => (int)$data['created_by'],
            'updated_by' => (int)($data['updated_by'] ?? $data['created_by'])
        ];
    }
}
