-- إعد<PERSON> قسم العملاء في قاعدة البيانات
-- يجب تشغيل هذا الـ script قبل استخدام وحدة العملاء

-- 1. التأكد من وجود قسم العملاء
INSERT IGNORE INTO sections (code, description) VALUES
('customers', 'العملاء الذين يتم البيع لهم');

-- 2. إنشاء مجموعة افتراضية للعملاء لكل شركة موجودة
INSERT IGNORE INTO entity_groups (company_id, section_id, group_number, name_ar, name_en, is_default, created_by, created_at)
SELECT 
    c.CompanyID as company_id,
    s.id as section_id,
    1 as group_number,
    'عملاء عامون' as name_ar,
    'General Customers' as name_en,
    TRUE as is_default,
    1 as created_by,
    NOW() as created_at
FROM companies c
CROSS JOIN sections s
WHERE s.code = 'customers'
AND NOT EXISTS (
    SELECT 1 FROM entity_groups eg 
    WHERE eg.company_id = c.CompanyID 
    AND eg.section_id = s.id
);

-- 3. عرض النتائج للتأكد
SELECT 
    s.id as section_id,
    s.code as section_code,
    s.description as section_description
FROM sections s 
WHERE s.code = 'customers';

SELECT 
    eg.id,
    eg.company_id,
    eg.section_id,
    eg.name_ar,
    eg.is_default,
    c.CompanyNameAR as company_name
FROM entity_groups eg
JOIN companies c ON eg.company_id = c.CompanyID
JOIN sections s ON eg.section_id = s.id
WHERE s.code = 'customers'
ORDER BY eg.company_id;
