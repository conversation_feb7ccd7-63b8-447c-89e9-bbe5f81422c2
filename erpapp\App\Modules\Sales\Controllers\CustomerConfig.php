<?php
namespace App\Modules\Sales\Controllers;

/**
 * CustomerConfig - إعدادات مبسطة للعملاء
 * 
 * ⭐ تم التحويل من الموردين إلى العملاء بنجاح ⭐
 */
class CustomerConfig
{
    // ==========================================
    // 🎯 المتغيرات المحولة للعملاء
    // ==========================================
    
    /**
     * 📝 أسماء الكيان (للعرض)
     */
    const ENTITY_NAME = 'العميل';
    const ENTITY_NAME_PLURAL = 'العملاء';
    
    /**
     * 🔤 أسماء الكيان (للكود)
     */
    const ENTITY_CODE = 'customer';
    const ENTITY_CODE_PLURAL = 'customers';
    
    /**
     * 🏢 اسم الوحدة
     */
    const MODULE_NAME = 'المبيعات';
    const MODULE_CODE = 'sales';
    
    /**
     * 🔗 مسارات الوحدة
     */
    const MODULE_URL = 'sales';
    const ENTITY_URL = 'sales/customers';
    
    /**
     * 📊 بادئة الحقول الخاصة
     */
    const FIELD_PREFIX = 'C_';
    
    /**
     * 📧 حقل البريد الإلكتروني
     */
    const EMAIL_FIELD = 'C_email';
    
    /**
     * 🏗️ أسماء النماذج
     */
    const MODEL_CLASS = 'App\Modules\Sales\Models\Customer';
    const GROUP_MODEL_CLASS = 'App\Modules\Sales\Models\CustomerGroup';
    
    /**
     * 👁️ أسماء المتغيرات في الـ views
     */
    const VIEW_ENTITY_VAR = 'customer';
    const VIEW_ENTITIES_VAR = 'customers';
    const VIEW_GROUPS_VAR = 'customerGroups';
    
    /**
     * 🔍 اسم الفلتر
     */
    const FILTER_NAME = 'customers';
    
    /**
     * 📁 إعدادات الـ views
     */
    const VIEW_NAMESPACE = 'Sales';
    const VIEW_FOLDER = 'customers';
    
    // ==========================================
    // 🤖 الدوال التلقائية (لا تحتاج تغيير)
    // ==========================================
    
    /**
     * الحقول المشتركة بين جميع الكيانات
     */
    public static function getCommonFields()
    {
        return [
            'group_id', 'G_name_ar', 'G_name_en', 'G_phone', 'G_mobile',
            'G_website', 'G_notes', 'G_status'
        ];
    }
    
    /**
     * الحقول الخاصة (تلقائية حسب البادئة)
     */
    public static function getSpecificFields()
    {
        $prefix = self::FIELD_PREFIX;
        return [
            $prefix . 'email', $prefix . 'tax_number', $prefix . 'commercial_register', 
            $prefix . 'credit_limit', $prefix . 'payment_terms', $prefix . 'discount_rate',
            $prefix . 'customer_type', $prefix . 'price_list', $prefix . 'sales_rep_id',
            $prefix . 'territory', $prefix . 'industry', $prefix . 'source', $prefix . 'rating'
        ];
    }
    
    /**
     * جميع الحقول للمقارنة
     */
    public static function getAllFields()
    {
        return array_merge(self::getCommonFields(), self::getSpecificFields());
    }
    
    /**
     * الحقول المطلوبة (تلقائية)
     */
    public static function getRequiredFields()
    {
        return [
            'G_name_ar' => 'اسم ' . self::ENTITY_NAME . ' بالعربية مطلوب'
        ];
    }
    
    /**
     * حقول البريد الإلكتروني للتحقق (تلقائية)
     */
    public static function getEmailFields()
    {
        return [
            self::EMAIL_FIELD => 'البريد الإلكتروني غير صحيح'
        ];
    }
    
    /**
     * حقول المواقع الإلكترونية للتحقق
     */
    public static function getWebsiteFields()
    {
        return [
            'G_website' => 'الموقع الإلكتروني غير صحيح'
        ];
    }
    
    /**
     * الحقول الرقمية للتحقق (تلقائية حسب البادئة)
     */
    public static function getNumericFields()
    {
        $prefix = self::FIELD_PREFIX;
        return [
            $prefix . 'payment_terms' => [
                'default' => 0,
                'min' => 0,
                'error' => 'شروط الدفع يجب أن تكون رقماً موجباً'
            ],
            $prefix . 'credit_limit' => [
                'default' => 0,
                'min' => 0,
                'error' => 'الحد الائتماني يجب أن يكون رقماً موجباً'
            ],
            $prefix . 'discount_rate' => [
                'default' => 0,
                'min' => 0,
                'max' => 100,
                'error' => 'معدل الخصم يجب أن يكون رقماً بين 0 و 100'
            ]
        ];
    }
    
    /**
     * القيم الافتراضية للحقول (تلقائية حسب البادئة)
     */
    public static function getDefaultValues()
    {
        $prefix = self::FIELD_PREFIX;
        return [
            'G_status' => 'active',
            $prefix . 'payment_terms' => 0,
            $prefix . 'credit_limit' => 0,
            $prefix . 'discount_rate' => 0,
            $prefix . 'customer_type' => 'individual',
            $prefix . 'rating' => 'C'
        ];
    }
    
    /**
     * خيارات الحالة
     */
    public static function getStatusOptions()
    {
        return [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ];
    }
    
    /**
     * خيارات التقييم
     */
    public static function getRatingOptions()
    {
        return [
            'A' => 'ممتاز',
            'B' => 'جيد جداً',
            'C' => 'جيد',
            'D' => 'مقبول'
        ];
    }
    
    /**
     * خيارات نوع العميل
     */
    public static function getCustomerTypeOptions()
    {
        return [
            'individual' => 'فردي',
            'company' => 'شركة',
            'government' => 'حكومي',
            'other' => 'أخرى'
        ];
    }
    
    /**
     * إعدادات الفلاتر الافتراضية
     */
    public static function getDefaultFilters()
    {
        return [
            'status' => '',
            'group_id' => ''
        ];
    }
    
    /**
     * حقول الفلاتر
     */
    public static function getFilterFields()
    {
        return ['search', 'status', 'group_id'];
    }
    
    /**
     * رسائل النجاح (تلقائية)
     */
    public static function getSuccessMessages()
    {
        return [
            'created' => 'تم إنشاء ' . self::ENTITY_NAME . ' بنجاح مع جميع البيانات',
            'updated' => 'تم تحديث ' . self::ENTITY_NAME . ' بنجاح - رقم ' . self::ENTITY_NAME . ': {number}',
            'deleted' => 'تم حذف ' . self::ENTITY_NAME . ' بنجاح'
        ];
    }
    
    /**
     * رسائل الخطأ (تلقائية)
     */
    public static function getErrorMessages()
    {
        return [
            'not_found' => self::ENTITY_NAME . ' غير موجود',
            'create_failed' => 'حدث خطأ أثناء إنشاء ' . self::ENTITY_NAME,
            'update_failed' => 'فشل في تحديث البيانات الأساسية لـ' . self::ENTITY_NAME,
            'delete_failed' => 'حدث خطأ أثناء حذف ' . self::ENTITY_NAME,
            'no_changes' => 'لا توجد تغييرات لحفظها',
            'duplicate_name' => 'اسم ' . self::ENTITY_NAME . ' موجود بالفعل في الشركة الحالية'
        ];
    }
    
    /**
     * عناوين الصفحات (تلقائية)
     */
    public static function getPageTitles()
    {
        return [
            'index' => self::ENTITY_NAME_PLURAL,
            'create' => 'إضافة ' . self::ENTITY_NAME . ' جديد',
            'show' => 'عرض ' . self::ENTITY_NAME . ' - {name}',
            'edit' => 'تعديل ' . self::ENTITY_NAME . ' - {name}',
            'stats' => 'إحصائيات ' . self::ENTITY_NAME_PLURAL,
            'mixed' => self::ENTITY_NAME_PLURAL . ' - عرض مختلط'
        ];
    }
    
    /**
     * إعدادات الـ breadcrumbs (تلقائية)
     */
    public static function getBreadcrumbConfig()
    {
        return [
            'module_title' => self::MODULE_NAME,
            'module_url' => self::MODULE_URL,
            'entity_title' => self::ENTITY_NAME_PLURAL,
            'entity_url' => self::ENTITY_URL
        ];
    }
    
    /**
     * إعدادات الـ views (تلقائية)
     */
    public static function getViewConfig()
    {
        return [
            'namespace' => self::VIEW_NAMESPACE,
            'folder' => self::VIEW_FOLDER,
            'entity_var' => self::VIEW_ENTITY_VAR,
            'entities_var' => self::VIEW_ENTITIES_VAR,
            'groups_var' => self::VIEW_GROUPS_VAR
        ];
    }
    
    /**
     * إعدادات الـ URLs (تلقائية)
     */
    public static function getUrlConfig()
    {
        return [
            'index' => self::ENTITY_URL,
            'create' => self::ENTITY_URL . '/create',
            'store' => self::ENTITY_URL . '/store',
            'show' => self::ENTITY_URL . '/{id}',
            'edit' => self::ENTITY_URL . '/{id}/edit',
            'update' => self::ENTITY_URL . '/{id}/update',
            'delete' => self::ENTITY_URL . '/{id}/delete',
            'stats' => self::ENTITY_URL . '/stats',
            'mixed' => self::ENTITY_URL . '/mixed',
            'apply_filters' => self::ENTITY_URL . '/apply-filters',
            'clear_filters' => self::ENTITY_URL . '/clear-filters'
        ];
    }
}
