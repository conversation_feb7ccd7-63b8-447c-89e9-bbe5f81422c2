<?php
$title = $supplierGroup['name_ar'];
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/'],
    ['title' => 'المشتريات', 'url' => '/purchases'],
    ['title' => 'مجموعات الموردين', 'url' => '/purchases/supplier-groups'],
    ['title' => $supplierGroup['name_ar'], 'url' => '']
];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if ($item['url']): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="header-title mb-0">تفاصيل المجموعة</h4>
                        <div>
                            <a href="<?= base_url('purchases/supplier-groups/' . $supplierGroup['group_number'] . '/edit') ?>" 
                               class="btn btn-success me-2">
                                <i class="mdi mdi-pencil me-1"></i> تعديل
                            </a>
                            <button type="button" class="btn btn-danger" 
                                    onclick="confirmDelete(<?= $supplierGroup['group_number'] ?>)">
                                <i class="mdi mdi-delete me-1"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Details -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">معلومات المجموعة</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-borderless mb-0">
                            <tbody>
                                <tr>
                                    <th scope="row" style="width: 30%;">رقم المجموعة:</th>
                                    <td><?= $supplierGroup['group_number'] ?></td>
                                </tr>
                                <tr>
                                    <th scope="row">اسم المجموعة (عربي):</th>
                                    <td><?= htmlspecialchars($supplierGroup['name_ar']) ?></td>
                                </tr>
                                <tr>
                                    <th scope="row">اسم المجموعة (إنجليزي):</th>
                                    <td><?= htmlspecialchars($supplierGroup['name_en'] ?: '-') ?></td>
                                </tr>
                                <tr>
                                    <th scope="row">تاريخ الإنشاء:</th>
                                    <td><?= date('Y-m-d H:i', strtotime($supplierGroup['created_at'])) ?></td>
                                </tr>
                                <?php if ($supplierGroup['updated_at']): ?>
                                <tr>
                                    <th scope="row">آخر تحديث:</th>
                                    <td><?= date('Y-m-d H:i', strtotime($supplierGroup['updated_at'])) ?></td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">إحصائيات المجموعة</h4>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="mdi mdi-truck text-primary" style="font-size: 3rem;"></i>
                        </div>
                        <h3 class="text-primary">0</h3>
                        <p class="text-muted mb-0">عدد الموردين في هذه المجموعة</p>
                    </div>
                    
                    <div class="mt-4">
                        <a href="<?= base_url('purchases/suppliers?group_id=' . $supplierGroup['group_number']) ?>" 
                           class="btn btn-primary btn-sm w-100">
                            <i class="mdi mdi-view-list me-1"></i> عرض موردين المجموعة
                        </a>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">إجراءات سريعة</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('purchases/suppliers/create?group_id=' . $supplierGroup['group_number']) ?>" 
                           class="btn btn-success btn-sm">
                            <i class="mdi mdi-plus-circle me-1"></i> إضافة مورد لهذه المجموعة
                        </a>
                        <a href="<?= base_url('purchases/supplier-groups') ?>" 
                           class="btn btn-light btn-sm">
                            <i class="mdi mdi-arrow-left me-1"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">تأكيد الحذف</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف مجموعة الموردين "<strong><?= htmlspecialchars($supplierGroup['name_ar']) ?></strong>"؟</p>
                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
                <div class="alert alert-warning">
                    <i class="mdi mdi-alert-circle me-1"></i>
                    تأكد من عدم وجود موردين في هذه المجموعة قبل الحذف.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف المجموعة</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(groupNumber) {
    document.getElementById('deleteForm').action = '<?= base_url('purchases/supplier-groups/') ?>' + groupNumber + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
