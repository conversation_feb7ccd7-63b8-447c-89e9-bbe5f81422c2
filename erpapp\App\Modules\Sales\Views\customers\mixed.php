<?php
/**
 * صفحة العرض المختلط للعملاء (إحصائيات + جدول)
 */

// تحديد العنوان والوصف
$page_title = $title ?? 'العملاء - عرض مختلط';
$page_description = 'عرض شامل للعملاء مع الإحصائيات والجدول';

// تضمين رأس الصفحة
include_once VIEWS_PATH . '/layouts/header.php';
?>

<div class="container-fluid">
    <!-- Breadcrumb -->
    <?php if (isset($breadcrumb) && !empty($breadcrumb)): ?>
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <?php foreach ($breadcrumb as $item): ?>
                    <?php if (isset($item['active']) && $item['active']): ?>
                        <li class="breadcrumb-item active" aria-current="page"><?= htmlspecialchars($item['title']) ?></li>
                    <?php else: ?>
                        <li class="breadcrumb-item">
                            <a href="<?= htmlspecialchars($item['url']) ?>"><?= htmlspecialchars($item['title']) ?></a>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    <?php endif; ?>

    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><?= htmlspecialchars($page_title) ?></h1>
        <div>
            <a href="<?= base_url('sales/customers') ?>" class="btn btn-secondary">
                <i class="fas fa-list"></i> عرض عادي
            </a>
            <a href="<?= base_url('sales/customers/stats') ?>" class="btn btn-info">
                <i class="fas fa-chart-bar"></i> الإحصائيات فقط
            </a>
            <a href="<?= base_url('sales/customers/create') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة عميل جديد
            </a>
        </div>
    </div>

    <!-- الإحصائيات المختصرة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي العملاء
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['total_customers'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                العملاء النشطين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['active_customers'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                العملاء الأفراد
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['individual_customers'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                عملاء الشركات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['company_customers'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-filter"></i> فلاتر البحث
            </h5>
        </div>
        <div class="card-body">
            <form method="POST" action="<?= base_url('sales/customers/apply-filters') ?>" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?= htmlspecialchars($filters['search'] ?? '') ?>" 
                           placeholder="البحث في الاسم أو البريد الإلكتروني">
                </div>
                
                <div class="col-md-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?= ($filters['status'] ?? '') === 'active' ? 'selected' : '' ?>>نشط</option>
                        <option value="inactive" <?= ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                        <option value="suspended" <?= ($filters['status'] ?? '') === 'suspended' ? 'selected' : '' ?>>معلق</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="group_id" class="form-label">المجموعة</label>
                    <select class="form-select" id="group_id" name="group_id">
                        <option value="">جميع المجموعات</option>
                        <?php if (isset($customerGroups) && !empty($customerGroups)): ?>
                            <?php foreach ($customerGroups as $group): ?>
                                <option value="<?= $group['id'] ?>" 
                                        <?= ($filters['group_id'] ?? '') == $group['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($group['name_ar']) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="<?= base_url('sales/customers/clear-filters') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> مسح
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول العملاء -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-users"></i> قائمة العملاء
                <?php if (isset($total_count)): ?>
                    <span class="badge bg-primary"><?= number_format($total_count) ?></span>
                <?php endif; ?>
            </h5>
            
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <a href="<?= base_url('sales/customers/export') ?>" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </a>
            </div>
        </div>
        
        <div class="card-body">
            <?php if (isset($customers) && !empty($customers)): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم العميل</th>
                                <th>اسم العميل</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>نوع العميل</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($customers as $customer): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($customer['entity_number']) ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($customer['G_name_ar']) ?></strong>
                                            <?php if (!empty($customer['G_name_en'])): ?>
                                                <br><small class="text-muted"><?= htmlspecialchars($customer['G_name_en']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (!empty($customer['C_email'])): ?>
                                            <a href="mailto:<?= htmlspecialchars($customer['C_email']) ?>">
                                                <?= htmlspecialchars($customer['C_email']) ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($customer['G_phone'])): ?>
                                            <a href="tel:<?= htmlspecialchars($customer['G_phone']) ?>">
                                                <?= htmlspecialchars($customer['G_phone']) ?>
                                            </a>
                                        <?php elseif (!empty($customer['G_mobile'])): ?>
                                            <a href="tel:<?= htmlspecialchars($customer['G_mobile']) ?>">
                                                <?= htmlspecialchars($customer['G_mobile']) ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $customerTypes = [
                                            'individual' => 'فردي',
                                            'company' => 'شركة',
                                            'government' => 'حكومي',
                                            'other' => 'أخرى'
                                        ];
                                        $typeLabel = $customerTypes[$customer['C_customer_type'] ?? 'individual'] ?? 'غير محدد';
                                        ?>
                                        <span class="badge bg-secondary"><?= $typeLabel ?></span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = [
                                            'active' => 'success',
                                            'inactive' => 'secondary',
                                            'suspended' => 'warning'
                                        ];
                                        $statusLabels = [
                                            'active' => 'نشط',
                                            'inactive' => 'غير نشط',
                                            'suspended' => 'معلق'
                                        ];
                                        $status = $customer['G_status'] ?? 'active';
                                        ?>
                                        <span class="badge bg-<?= $statusClass[$status] ?? 'secondary' ?>">
                                            <?= $statusLabels[$status] ?? 'غير محدد' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('sales/customers/' . $customer['entity_number']) ?>" 
                                               class="btn btn-sm btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('sales/customers/' . $customer['entity_number'] . '/edit') ?>" 
                                               class="btn btn-sm btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if (isset($pagination) && !empty($pagination)): ?>
                    <nav aria-label="Customer pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?= $pagination ?>
                        </ul>
                    </nav>
                <?php endif; ?>

            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد عملاء</h5>
                    <p class="text-muted">لم يتم العثور على أي عملاء مطابقين للفلاتر المحددة</p>
                    <a href="<?= base_url('sales/customers/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة عميل جديد
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// تضمين تذييل الصفحة
include_once VIEWS_PATH . '/layouts/footer.php';
?>
