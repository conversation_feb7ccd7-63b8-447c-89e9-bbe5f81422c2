<?php
namespace App\Modules\Purchases\Controllers;

use App\Modules\Purchases\Models\Supplier;
use App\Modules\Purchases\Models\SupplierGroup;
use Exception;
use PDO;

/**
 * PurchaseController - متحكم لوحة تحكم المشتريات
 */
class PurchaseController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Supplier model
     */
    protected $supplierModel;

    /**
     * SupplierGroup model
     */
    protected $supplierGroupModel;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->supplierModel = new Supplier();
        $this->supplierGroupModel = new SupplierGroup();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash('purchase_error', 'يجب تحديد شركة حالية للوصول إلى المشتريات', 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * لوحة تحكم المشتريات
     */
    public function index()
    {
        $company_id = current_user()['current_company_id'];

        // الحصول على الإحصائيات
        $supplierStats = $this->supplierModel->getStats($company_id);
        $groupStats = $this->supplierGroupModel->getStats($company_id);

        // الحصول على آخر الموردين المضافين
        $recentSuppliers = $this->supplierModel->getByCompany($company_id, [
            'limit' => 5,
            'order_by' => 's.created_at DESC'
        ]);

        // إحصائيات عامة
        $stats = [
            'total_suppliers' => $supplierStats['total_suppliers'],
            'active_suppliers' => $supplierStats['active_suppliers'],
            'total_groups' => $groupStats['total_groups'],
            'inactive_suppliers' => $supplierStats['total_suppliers'] - $supplierStats['active_suppliers']
        ];

        // عرض لوحة التحكم
        $data = [
            'title' => 'لوحة تحكم المشتريات',
            'stats' => $stats,
            'recentSuppliers' => $recentSuppliers,
            'breadcrumb' => [
                ['title' => 'المشتريات', 'active' => true]
            ]
        ];

        view('Purchases::dashboard/index', $data);
    }

    /**
     * لوحة تحكم المشتريات (نفس index)
     */
    public function dashboard()
    {
        return $this->index();
    }
}
