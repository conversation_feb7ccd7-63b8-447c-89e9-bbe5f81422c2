<?php
/**
 * صفحة إحصائيات العملاء
 */

// تحديد العنوان والوصف
$page_title = $title ?? 'إحصائيات العملاء';
$page_description = 'إحصائيات شاملة عن العملاء والمبيعات';

// تضمين رأس الصفحة
include_once VIEWS_PATH . '/layouts/header.php';
?>

<div class="container-fluid">
    <!-- Breadcrumb -->
    <?php if (isset($breadcrumb) && !empty($breadcrumb)): ?>
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <?php foreach ($breadcrumb as $item): ?>
                    <?php if (isset($item['active']) && $item['active']): ?>
                        <li class="breadcrumb-item active" aria-current="page"><?= htmlspecialchars($item['title']) ?></li>
                    <?php else: ?>
                        <li class="breadcrumb-item">
                            <a href="<?= htmlspecialchars($item['url']) ?>"><?= htmlspecialchars($item['title']) ?></a>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    <?php endif; ?>

    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><?= htmlspecialchars($page_title) ?></h1>
        <div>
            <a href="<?= base_url('sales/customers') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
            <a href="<?= base_url('sales/customers/create') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة عميل جديد
            </a>
        </div>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي العملاء
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['total_customers'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                العملاء النشطين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['active_customers'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                العملاء الأفراد
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['individual_customers'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                عملاء الشركات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['company_customers'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row">
        <!-- توزيع العملاء حسب النوع -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">توزيع العملاء حسب النوع</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="customerTypeChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="mr-2">
                            <i class="fas fa-circle text-primary"></i> أفراد
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-success"></i> شركات
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-info"></i> حكومي
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- توزيع العملاء حسب الحالة -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">توزيع العملاء حسب الحالة</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="customerStatusChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="mr-2">
                            <i class="fas fa-circle text-success"></i> نشط
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-secondary"></i> غير نشط
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-warning"></i> معلق
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول العملاء الأحدث -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">العملاء المضافون حديثاً</h6>
        </div>
        <div class="card-body">
            <?php if (isset($recent_customers) && !empty($recent_customers)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>رقم العميل</th>
                                <th>اسم العميل</th>
                                <th>نوع العميل</th>
                                <th>تاريخ الإضافة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_customers as $customer): ?>
                                <tr>
                                    <td><?= htmlspecialchars($customer['entity_number']) ?></td>
                                    <td><?= htmlspecialchars($customer['G_name_ar']) ?></td>
                                    <td>
                                        <?php
                                        $types = [
                                            'individual' => 'فردي',
                                            'company' => 'شركة',
                                            'government' => 'حكومي'
                                        ];
                                        echo $types[$customer['C_customer_type']] ?? 'غير محدد';
                                        ?>
                                    </td>
                                    <td><?= date('Y-m-d', strtotime($customer['created_at'])) ?></td>
                                    <td>
                                        <?php
                                        $statusClass = [
                                            'active' => 'success',
                                            'inactive' => 'secondary',
                                            'suspended' => 'warning'
                                        ];
                                        $statusLabels = [
                                            'active' => 'نشط',
                                            'inactive' => 'غير نشط',
                                            'suspended' => 'معلق'
                                        ];
                                        $status = $customer['G_status'] ?? 'active';
                                        ?>
                                        <span class="badge badge-<?= $statusClass[$status] ?? 'secondary' ?>">
                                            <?= $statusLabels[$status] ?? 'غير محدد' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('sales/customers/' . $customer['entity_number']) ?>" 
                                           class="btn btn-sm btn-primary">عرض</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات</h5>
                    <p class="text-muted">لم يتم إضافة أي عملاء حتى الآن</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- تضمين مكتبة Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// رسم بياني لتوزيع العملاء حسب النوع
var ctx1 = document.getElementById("customerTypeChart");
var customerTypeChart = new Chart(ctx1, {
    type: 'doughnut',
    data: {
        labels: ["أفراد", "شركات", "حكومي"],
        datasets: [{
            data: [
                <?= $stats['individual_customers'] ?? 0 ?>,
                <?= $stats['company_customers'] ?? 0 ?>,
                <?= $stats['government_customers'] ?? 0 ?>
            ],
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        maintainAspectRatio: false,
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
        },
        legend: {
            display: false
        },
        cutoutPercentage: 80,
    },
});

// رسم بياني لتوزيع العملاء حسب الحالة
var ctx2 = document.getElementById("customerStatusChart");
var customerStatusChart = new Chart(ctx2, {
    type: 'doughnut',
    data: {
        labels: ["نشط", "غير نشط", "معلق"],
        datasets: [{
            data: [
                <?= $stats['active_customers'] ?? 0 ?>,
                <?= ($stats['total_customers'] ?? 0) - ($stats['active_customers'] ?? 0) ?>,
                0
            ],
            backgroundColor: ['#1cc88a', '#858796', '#f6c23e'],
            hoverBackgroundColor: ['#17a673', '#5a5c69', '#dda20a'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        maintainAspectRatio: false,
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
        },
        legend: {
            display: false
        },
        cutoutPercentage: 80,
    },
});
</script>

<?php
// تضمين تذييل الصفحة
include_once VIEWS_PATH . '/layouts/footer.php';
?>
