<?php
// تضمين القالب المشترك للحصول على $form_tabs
include __DIR__ . '/form_template.php';

// إعداد الإجراءات
$actions = [
    [
        'type' => 'secondary',
        'url' => 'sales/customers',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'primary',
        'form' => 'customerForm',
        'icon' => 'fas fa-save',
        'text' => 'حفظ العميل',
        'submit' => true
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المبيعات', 'url' => 'sales'],
    ['title' => 'العملاء', 'url' => 'sales/customers'],
    ['title' => 'إضافة عميل', 'active' => true]
];

// استخدام النظام الموحد
render_form_page([
    'title' => $title ?? 'إضافة عميل جديد',
    'module' => 'sales',
    'entity' => 'customers',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    // النموذج بالتبويبات الديناميكية
    'form' => [
        'action' => base_url('sales/customers/store'),
        'method' => 'POST',
        'id' => 'customerForm',
        'tabs' => $form_tabs
    ]
]);
?>
