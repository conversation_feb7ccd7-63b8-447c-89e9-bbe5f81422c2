<?php
namespace App\Modules\Purchases\Controllers;

/**
 * SupplierConfig - إعدادات مبسطة للموردين
 * 
 * ⭐ للتحويل للعملاء: غيّر فقط المتغيرات في الأعلى وسيتم تطبيق التغيير على كل شيء ⭐
 */
class SupplierConfig
{
    // ==========================================
    // 🎯 المتغيرات الوحيدة التي تحتاج تغيير
    // ==========================================
    
    /**
     * 📝 أسماء الكيان (للعرض)
     */
    const ENTITY_NAME = 'المورد';                    // للعملاء: 'العميل'
    const ENTITY_NAME_PLURAL = 'الموردين';           // للعملاء: 'العملاء'
    
    /**
     * 🔤 أسماء الكيان (للكود)
     */
    const ENTITY_CODE = 'supplier';                  // للعملاء: 'customer'
    const ENTITY_CODE_PLURAL = 'suppliers';          // للعملاء: 'customers'
    
    /**
     * 🏢 اسم الوحدة
     */
    const MODULE_NAME = 'المشتريات';                 // للعملاء: 'المبيعات'
    const MODULE_CODE = 'purchases';                 // للعملاء: 'sales'
    
    /**
     * 🔗 مسارات الوحدة
     */
    const MODULE_URL = 'purchases';                  // للعملاء: 'sales'
    const ENTITY_URL = 'purchases/suppliers';        // للعملاء: 'sales/customers'
    
    /**
     * 📊 بادئة الحقول الخاصة
     */
    const FIELD_PREFIX = 'S_';                       // للعملاء: 'C_'
    
    /**
     * 📧 حقل البريد الإلكتروني
     */
    const EMAIL_FIELD = 'S_email';                   // للعملاء: 'C_email'
    
    /**
     * 🏗️ أسماء النماذج
     */
    const MODEL_CLASS = 'App\Modules\Purchases\Models\Supplier';           // للعملاء: 'App\Modules\Sales\Models\Customer'
    const GROUP_MODEL_CLASS = 'App\Modules\Purchases\Models\SupplierGroup'; // للعملاء: 'App\Modules\Sales\Models\CustomerGroup'
    
    /**
     * 👁️ أسماء المتغيرات في الـ views
     */
    const VIEW_ENTITY_VAR = 'supplier';              // للعملاء: 'customer'
    const VIEW_ENTITIES_VAR = 'suppliers';           // للعملاء: 'customers'
    const VIEW_GROUPS_VAR = 'supplierGroups';        // للعملاء: 'customerGroups'
    
    /**
     * 🔍 اسم الفلتر
     */
    const FILTER_NAME = 'suppliers';                 // للعملاء: 'customers'
    
    /**
     * 📁 إعدادات الـ views
     */
    const VIEW_NAMESPACE = 'Purchases';              // للعملاء: 'Sales'
    const VIEW_FOLDER = 'suppliers';                 // للعملاء: 'customers'
    
    // ==========================================
    // 🤖 الدوال التلقائية (لا تحتاج تغيير)
    // ==========================================
    
    /**
     * الحقول المشتركة بين جميع الكيانات
     */
    public static function getCommonFields()
    {
        return [
            'group_id', 'G_name_ar', 'G_name_en', 'G_phone', 'G_mobile',
            'G_website', 'G_notes', 'G_status'
        ];
    }
    
    /**
     * الحقول الخاصة (تلقائية حسب البادئة)
     */
    public static function getSpecificFields()
    {
        $prefix = self::FIELD_PREFIX;
        return [
            $prefix . 'company_name', $prefix . 'contact_person', $prefix . 'email', $prefix . 'tax_number', 
            $prefix . 'commercial_register', $prefix . 'payment_terms', $prefix . 'credit_limit', 
            $prefix . 'discount_rate', $prefix . 'delivery_time', $prefix . 'minimum_order',
            $prefix . 'currency', $prefix . 'rating', $prefix . 'license_number', $prefix . 'license_expiry',
            $prefix . 'establishment_date', $prefix . 'legal_form', $prefix . 'internal_notes', $prefix . 'special_instructions'
        ];
    }
    
    /**
     * جميع الحقول للمقارنة
     */
    public static function getAllFields()
    {
        return array_merge(self::getCommonFields(), self::getSpecificFields());
    }
    
    /**
     * الحقول المطلوبة (تلقائية)
     */
    public static function getRequiredFields()
    {
        return [
            'G_name_ar' => 'اسم ' . self::ENTITY_NAME . ' بالعربية مطلوب'
        ];
    }
    
    /**
     * حقول البريد الإلكتروني للتحقق (تلقائية)
     */
    public static function getEmailFields()
    {
        return [
            self::EMAIL_FIELD => 'البريد الإلكتروني غير صحيح'
        ];
    }
    
    /**
     * حقول المواقع الإلكترونية للتحقق
     */
    public static function getWebsiteFields()
    {
        return [
            'G_website' => 'الموقع الإلكتروني غير صحيح'
        ];
    }
    
    /**
     * الحقول الرقمية للتحقق (تلقائية حسب البادئة)
     */
    public static function getNumericFields()
    {
        $prefix = self::FIELD_PREFIX;
        return [
            $prefix . 'payment_terms' => [
                'default' => 30,
                'min' => 0,
                'error' => 'شروط الدفع يجب أن تكون رقماً موجباً'
            ],
            $prefix . 'credit_limit' => [
                'default' => 0,
                'min' => 0,
                'error' => 'الحد الائتماني يجب أن يكون رقماً موجباً'
            ],
            $prefix . 'discount_rate' => [
                'default' => 0,
                'min' => 0,
                'max' => 100,
                'error' => 'معدل الخصم يجب أن يكون رقماً بين 0 و 100'
            ],
            $prefix . 'delivery_time' => [
                'default' => null,
                'min' => 0,
                'error' => 'مدة التسليم يجب أن تكون رقماً موجباً'
            ],
            $prefix . 'minimum_order' => [
                'default' => null,
                'min' => 0,
                'error' => 'الحد الأدنى للطلب يجب أن يكون رقماً موجباً'
            ]
        ];
    }
    
    /**
     * القيم الافتراضية للحقول (تلقائية حسب البادئة)
     */
    public static function getDefaultValues()
    {
        $prefix = self::FIELD_PREFIX;
        return [
            'G_status' => 'active',
            $prefix . 'payment_terms' => 30,
            $prefix . 'credit_limit' => 0,
            $prefix . 'discount_rate' => 0,
            $prefix . 'currency' => 'SAR',
            $prefix . 'rating' => 'C'
        ];
    }
    
    /**
     * خيارات الحالة
     */
    public static function getStatusOptions()
    {
        return [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ];
    }
    
    /**
     * خيارات التقييم
     */
    public static function getRatingOptions()
    {
        return [
            'A' => 'ممتاز',
            'B' => 'جيد جداً',
            'C' => 'جيد',
            'D' => 'مقبول'
        ];
    }
    
    /**
     * خيارات العملة
     */
    public static function getCurrencyOptions()
    {
        return [
            'SAR' => 'ريال سعودي',
            'USD' => 'دولار أمريكي',
            'EUR' => 'يورو',
            'GBP' => 'جنيه إسترليني'
        ];
    }
    
    /**
     * خيارات الشكل القانوني
     */
    public static function getLegalFormOptions()
    {
        return [
            'individual' => 'فردي',
            'llc' => 'شركة ذات مسؤولية محدودة',
            'corporation' => 'شركة مساهمة',
            'partnership' => 'شراكة',
            'other' => 'أخرى'
        ];
    }
    
    /**
     * إعدادات الفلاتر الافتراضية
     */
    public static function getDefaultFilters()
    {
        return [
            'status' => '',
            'group_id' => ''
        ];
    }
    
    /**
     * حقول الفلاتر
     */
    public static function getFilterFields()
    {
        return ['search', 'status', 'group_id'];
    }
    
    /**
     * رسائل النجاح (تلقائية)
     */
    public static function getSuccessMessages()
    {
        return [
            'created' => 'تم إنشاء ' . self::ENTITY_NAME . ' بنجاح مع جميع البيانات',
            'updated' => 'تم تحديث ' . self::ENTITY_NAME . ' بنجاح - رقم ' . self::ENTITY_NAME . ': {number}',
            'deleted' => 'تم حذف ' . self::ENTITY_NAME . ' بنجاح'
        ];
    }
    
    /**
     * رسائل الخطأ (تلقائية)
     */
    public static function getErrorMessages()
    {
        return [
            'not_found' => self::ENTITY_NAME . ' غير موجود',
            'create_failed' => 'حدث خطأ أثناء إنشاء ' . self::ENTITY_NAME,
            'update_failed' => 'فشل في تحديث البيانات الأساسية لـ' . self::ENTITY_NAME,
            'delete_failed' => 'حدث خطأ أثناء حذف ' . self::ENTITY_NAME,
            'no_changes' => 'لا توجد تغييرات لحفظها',
            'duplicate_name' => 'اسم ' . self::ENTITY_NAME . ' موجود بالفعل في الشركة الحالية'
        ];
    }
    
    /**
     * عناوين الصفحات (تلقائية)
     */
    public static function getPageTitles()
    {
        return [
            'index' => self::ENTITY_NAME_PLURAL,
            'create' => 'إضافة ' . self::ENTITY_NAME . ' جديد',
            'show' => 'عرض ' . self::ENTITY_NAME . ' - {name}',
            'edit' => 'تعديل ' . self::ENTITY_NAME . ' - {name}',
            'stats' => 'إحصائيات ' . self::ENTITY_NAME_PLURAL,
            'mixed' => self::ENTITY_NAME_PLURAL . ' - عرض مختلط'
        ];
    }
    
    /**
     * إعدادات الـ breadcrumbs (تلقائية)
     */
    public static function getBreadcrumbConfig()
    {
        return [
            'module_title' => self::MODULE_NAME,
            'module_url' => self::MODULE_URL,
            'entity_title' => self::ENTITY_NAME_PLURAL,
            'entity_url' => self::ENTITY_URL
        ];
    }
    
    /**
     * إعدادات الـ views (تلقائية)
     */
    public static function getViewConfig()
    {
        return [
            'namespace' => self::VIEW_NAMESPACE,
            'folder' => self::VIEW_FOLDER,
            'entity_var' => self::VIEW_ENTITY_VAR,
            'entities_var' => self::VIEW_ENTITIES_VAR,
            'groups_var' => self::VIEW_GROUPS_VAR
        ];
    }
    
    /**
     * إعدادات الـ URLs (تلقائية)
     */
    public static function getUrlConfig()
    {
        return [
            'index' => self::ENTITY_URL,
            'create' => self::ENTITY_URL . '/create',
            'store' => self::ENTITY_URL . '/store',
            'show' => self::ENTITY_URL . '/{id}',
            'edit' => self::ENTITY_URL . '/{id}/edit',
            'update' => self::ENTITY_URL . '/{id}/update',
            'delete' => self::ENTITY_URL . '/{id}/delete',
            'stats' => self::ENTITY_URL . '/stats',
            'mixed' => self::ENTITY_URL . '/mixed',
            'apply_filters' => self::ENTITY_URL . '/apply-filters',
            'clear_filters' => self::ENTITY_URL . '/clear-filters'
        ];
    }
}
