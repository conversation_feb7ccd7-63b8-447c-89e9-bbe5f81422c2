<?php
namespace App\Modules\Sales\Controllers;

use Exception;

/**
 * CustomerValidation - التحقق من صحة بيانات العملاء
 * 
 * يحتوي على جميع دوال التحقق من صحة البيانات الخاصة بالعملاء
 * تم التحويل من الموردين إلى العملاء
 */
class CustomerValidation
{
    /**
     * النموذج للتحقق من التكرار
     */
    protected $model;
    
    /**
     * Constructor
     */
    public function __construct($model)
    {
        $this->model = $model;
    }
    
    /**
     * التحقق من صحة بيانات العميل
     */
    public function validateCustomerData($data, $excludeEntityNumber = null)
    {
        $errors = [];
        
        // التحقق من الحقول المطلوبة
        $errors = array_merge($errors, $this->validateRequiredFields($data));
        
        // التحقق من تكرار الاسم
        $errors = array_merge($errors, $this->validateUniqueName($data, $excludeEntityNumber));
        
        // التحقق من البريد الإلكتروني
        $errors = array_merge($errors, $this->validateEmailFields($data));
        
        // التحقق من المواقع الإلكترونية
        $errors = array_merge($errors, $this->validateWebsiteFields($data));
        
        // التحقق من الحقول الرقمية
        $errors = array_merge($errors, $this->validateNumericFields($data));
        
        // التحقق من التواريخ
        $errors = array_merge($errors, $this->validateDateFields($data));
        
        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }
        
        // إرجاع البيانات المنظفة
        return $this->sanitizeData($data);
    }
    
    /**
     * التحقق من الحقول المطلوبة
     */
    protected function validateRequiredFields($data)
    {
        $errors = [];
        $requiredFields = CustomerConfig::getRequiredFields();
        
        foreach ($requiredFields as $field => $errorMessage) {
            if (empty($data[$field])) {
                $errors[] = $errorMessage;
            }
        }
        
        return $errors;
    }
    
    /**
     * التحقق من تكرار الاسم
     */
    protected function validateUniqueName($data, $excludeEntityNumber = null)
    {
        $errors = [];
        
        if (!empty($data['G_name_ar'])) {
            $company_id = current_user()['current_company_id'];
            $existing_entity = $this->model->findByNameAndCompany($data['G_name_ar'], $company_id);
            
            // إذا وُجد كيان بنفس الاسم وليس هو نفس الكيان الحالي (في حالة التعديل)
            if ($existing_entity && (!$excludeEntityNumber || $existing_entity['entity_number'] != $excludeEntityNumber)) {
                $errors[] = CustomerConfig::getErrorMessages()['duplicate_name'];
            }
        }
        
        return $errors;
    }
    
    /**
     * التحقق من حقول البريد الإلكتروني
     */
    protected function validateEmailFields($data)
    {
        $errors = [];
        $emailFields = CustomerConfig::getEmailFields();
        
        foreach ($emailFields as $field => $errorMessage) {
            if (!empty($data[$field]) && !filter_var($data[$field], FILTER_VALIDATE_EMAIL)) {
                $errors[] = $errorMessage;
            }
        }
        
        return $errors;
    }
    
    /**
     * التحقق من حقول المواقع الإلكترونية
     */
    protected function validateWebsiteFields($data)
    {
        $errors = [];
        $websiteFields = CustomerConfig::getWebsiteFields();
        
        foreach ($websiteFields as $field => $errorMessage) {
            if (!empty($data[$field]) && !filter_var($data[$field], FILTER_VALIDATE_URL)) {
                $errors[] = $errorMessage;
            }
        }
        
        return $errors;
    }
    
    /**
     * التحقق من الحقول الرقمية
     */
    protected function validateNumericFields($data)
    {
        $errors = [];
        $numericFields = CustomerConfig::getNumericFields();
        
        foreach ($numericFields as $field => $config) {
            if (!empty($data[$field])) {
                $value = $data[$field];
                
                // التحقق من أن القيمة رقمية
                if (!is_numeric($value)) {
                    $errors[] = $config['error'];
                    continue;
                }
                
                $numericValue = (float)$value;
                
                // التحقق من الحد الأدنى
                if (isset($config['min']) && $numericValue < $config['min']) {
                    $errors[] = $config['error'];
                }
                
                // التحقق من الحد الأقصى
                if (isset($config['max']) && $numericValue > $config['max']) {
                    $errors[] = $config['error'];
                }
            }
        }
        
        return $errors;
    }
    
    /**
     * التحقق من حقول التواريخ
     */
    protected function validateDateFields($data)
    {
        $errors = [];
        
        // يمكن إضافة تحقق من تواريخ خاصة بالعملاء هنا
        
        return $errors;
    }
    
    /**
     * التحقق من صحة التاريخ
     */
    protected function isValidDate($date, $format = 'Y-m-d')
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
    
    /**
     * تنظيف وإعداد البيانات
     */
    protected function sanitizeData($data)
    {
        $sanitized = [];
        $defaultValues = CustomerConfig::getDefaultValues();
        
        // الحقول المشتركة
        $commonFields = CustomerConfig::getCommonFields();
        foreach ($commonFields as $field) {
            if ($field === 'group_id') {
                $sanitized[$field] = !empty($data[$field]) ? (int)$data[$field] : null;
            } else {
                $sanitized[$field] = trim($data[$field] ?? '');
            }
        }
        
        // الحقول الخاصة بالعملاء
        $specificFields = CustomerConfig::getSpecificFields();
        foreach ($specificFields as $field) {
            if (in_array($field, ['C_payment_terms'])) {
                // حقول رقمية صحيحة
                $sanitized[$field] = !empty($data[$field]) ? (int)$data[$field] : ($defaultValues[$field] ?? null);
            } elseif (in_array($field, ['C_credit_limit', 'C_discount_rate'])) {
                // حقول رقمية عشرية
                $sanitized[$field] = !empty($data[$field]) ? (float)$data[$field] : ($defaultValues[$field] ?? null);
            } else {
                // حقول نصية
                $sanitized[$field] = trim($data[$field] ?? '');
            }
        }
        
        // تطبيق القيم الافتراضية
        foreach ($defaultValues as $field => $defaultValue) {
            if (!isset($sanitized[$field]) || $sanitized[$field] === '') {
                $sanitized[$field] = $defaultValue;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * التحقق من صحة بيانات العنوان
     */
    public function validateAddress($address)
    {
        $errors = [];
        
        // الحقول المطلوبة
        $requiredFields = ['address_type', 'address_label', 'address_line1', 'city'];
        
        foreach ($requiredFields as $field) {
            if (empty($address[$field])) {
                $fieldNames = [
                    'address_type' => 'نوع العنوان',
                    'address_label' => 'تسمية العنوان',
                    'address_line1' => 'العنوان الأول',
                    'city' => 'المدينة'
                ];
                $errors[] = $fieldNames[$field] . ' مطلوب';
            }
        }
        
        // التحقق من نوع العنوان
        $validTypes = ['main', 'billing', 'shipping', 'office', 'home', 'warehouse', 'other'];
        if (!empty($address['address_type']) && !in_array($address['address_type'], $validTypes)) {
            $errors[] = 'نوع العنوان غير صحيح';
        }
        
        // التحقق من الرمز البريدي
        if (!empty($address['postal_code']) && !preg_match('/^[0-9]{5}$/', $address['postal_code'])) {
            $errors[] = 'الرمز البريدي يجب أن يكون 5 أرقام';
        }
        
        return $errors;
    }
    
    /**
     * التحقق من صحة بيانات الحساب البنكي
     */
    public function validateBankAccount($account)
    {
        $errors = [];
        
        // الحقول المطلوبة
        $requiredFields = ['bank_name', 'account_number', 'account_name'];
        
        foreach ($requiredFields as $field) {
            if (empty($account[$field])) {
                $fieldNames = [
                    'bank_name' => 'اسم البنك',
                    'account_number' => 'رقم الحساب',
                    'account_name' => 'اسم صاحب الحساب'
                ];
                $errors[] = $fieldNames[$field] . ' مطلوب';
            }
        }
        
        // التحقق من رقم الحساب
        if (!empty($account['account_number'])) {
            $cleanAccountNumber = preg_replace('/[^0-9]/', '', $account['account_number']);
            if (strlen($cleanAccountNumber) < 8 || strlen($cleanAccountNumber) > 20) {
                $errors[] = 'رقم الحساب يجب أن يكون بين 8 و 20 رقم';
            }
        }
        
        // التحقق من رقم الآيبان
        if (!empty($account['iban']) && !$this->validateIBAN($account['iban'])) {
            $errors[] = 'رقم الآيبان غير صحيح';
        }
        
        return $errors;
    }
    
    /**
     * التحقق من صحة رقم الآيبان
     */
    protected function validateIBAN($iban)
    {
        // إزالة المسافات وتحويل إلى أحرف كبيرة
        $iban = strtoupper(str_replace(' ', '', $iban));
        
        // التحقق من الطول (بين 15 و 34 حرف)
        if (strlen($iban) < 15 || strlen($iban) > 34) {
            return false;
        }
        
        // التحقق من أن الآيبان يبدأ بحرفين ثم رقمين
        if (!preg_match('/^[A-Z]{2}[0-9]{2}/', $iban)) {
            return false;
        }
        
        // للآيبان السعودي - يجب أن يبدأ بـ SA ويكون 24 حرف
        if (substr($iban, 0, 2) === 'SA' && strlen($iban) !== 24) {
            return false;
        }
        
        return true;
    }
}
