<?php
namespace App\Modules\Purchases;

use App\Core\Module as BaseModule;

/**
 * وحدة المشتريات
 */
class Module extends BaseModule
{
    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // مسارات لوحة تحكم المشتريات
        add_route('GET', '/purchases', 'App\Modules\Purchases\Controllers\PurchaseController@index');

        // مسارات مجموعات الموردين
        add_route('GET', '/purchases/supplier-groups', 'App\Modules\Purchases\Controllers\SupplierGroupController@index');
        add_route('POST', '/purchases/supplier-groups/apply-filters', 'App\Modules\Purchases\Controllers\SupplierGroupController@applyFilters');
        add_route('GET', '/purchases/supplier-groups/clear-filters', 'App\Modules\Purchases\Controllers\SupplierGroupController@clearFilters');
        add_route('GET', '/purchases/supplier-groups/create', 'App\Modules\Purchases\Controllers\SupplierGroupController@create');
        add_route('POST', '/purchases/supplier-groups/store', 'App\Modules\Purchases\Controllers\SupplierGroupController@store');
        add_route('GET', '/purchases/supplier-groups/{id}', 'App\Modules\Purchases\Controllers\SupplierGroupController@show');
        add_route('GET', '/purchases/supplier-groups/{id}/edit', 'App\Modules\Purchases\Controllers\SupplierGroupController@edit');
        add_route('POST', '/purchases/supplier-groups/{id}/update', 'App\Modules\Purchases\Controllers\SupplierGroupController@update');
        add_route('POST', '/purchases/supplier-groups/{id}/delete', 'App\Modules\Purchases\Controllers\SupplierGroupController@delete');

        // مسارات الموردين - الروابط الثابتة أولاً
        add_route('GET', '/purchases/suppliers', 'App\Modules\Purchases\Controllers\SupplierController@index');
        add_route('POST', '/purchases/suppliers/apply-filters', 'App\Modules\Purchases\Controllers\SupplierController@applyFilters');
        add_route('GET', '/purchases/suppliers/clear-filters', 'App\Modules\Purchases\Controllers\SupplierController@clearFilters');
        add_route('GET', '/purchases/suppliers/stats', 'App\Modules\Purchases\Controllers\SupplierController@stats');
        add_route('GET', '/purchases/suppliers/mixed', 'App\Modules\Purchases\Controllers\SupplierController@mixed');
        add_route('GET', '/purchases/suppliers/dashboard', 'App\Modules\Purchases\Controllers\SupplierController@dashboard');
        add_route('GET', '/purchases/suppliers/create', 'App\Modules\Purchases\Controllers\SupplierController@create');
        add_route('POST', '/purchases/suppliers/store', 'App\Modules\Purchases\Controllers\SupplierController@store');

        // مسارات CSV للموردين - قبل الروابط المتغيرة
        add_route('GET', '/purchases/suppliers/export', 'App\Modules\Purchases\Controllers\CsvController@exportSuppliers');
        add_route('GET', '/purchases/suppliers/template', 'App\Modules\Purchases\Controllers\CsvController@downloadSuppliersTemplate');
        add_route('POST', '/purchases/suppliers/upload', 'App\Modules\Purchases\Controllers\CsvController@uploadSuppliersFile');
        add_route('POST', '/purchases/suppliers/preview', 'App\Modules\Purchases\Controllers\CsvController@previewSuppliersImport');
        add_route('POST', '/purchases/suppliers/import', 'App\Modules\Purchases\Controllers\CsvController@executeSuppliersImport');

        // الروابط المتغيرة أخيراً
        add_route('GET', '/purchases/suppliers/{id}', 'App\Modules\Purchases\Controllers\SupplierController@show');
        add_route('GET', '/purchases/suppliers/{id}/edit', 'App\Modules\Purchases\Controllers\SupplierController@edit');
        add_route('POST', '/purchases/suppliers/{id}/update', 'App\Modules\Purchases\Controllers\SupplierController@update');
        add_route('POST', '/purchases/suppliers/{id}/delete', 'App\Modules\Purchases\Controllers\SupplierController@delete');


    }
}