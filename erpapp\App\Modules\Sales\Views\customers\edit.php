<?php
// تضمين القالب المشترك للحصول على $form_tabs
include __DIR__ . '/form_template.php';

// إعداد الإجراءات للتعديل
$actions = [
    [
        'type' => 'secondary',
        'url' => 'sales/customers',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'info',
        'url' => 'sales/customers/' . ($customer['entity_number'] ?? ''),
        'icon' => 'fas fa-eye',
        'text' => 'عرض العميل'
    ],
    [
        'type' => 'primary',
        'form' => 'customerForm',
        'icon' => 'fas fa-save',
        'text' => 'حفظ التعديلات',
        'submit' => true
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المبيعات', 'url' => 'sales'],
    ['title' => 'العملاء', 'url' => 'sales/customers'],
    ['title' => $customer['G_name_ar'] ?? 'تعديل العميل', 'active' => true]
];

// استخدام النظام الموحد مع بيانات العميل الحالية
render_form_page([
    'title' => $title ?? 'تعديل العميل',
    'module' => 'sales',
    'entity' => 'customers',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    'form_data' => $customer ?? [], // بيانات العميل الحالية
    // النموذج بالتبويبات الديناميكية
    'form' => [
        'action' => base_url('sales/customers/' . ($customer['entity_number'] ?? '') . '/update'),
        'method' => 'POST',
        'id' => 'customerForm',
        'tabs' => $form_tabs,
        'attributes' => [
            'data-track-changes' => 'true'
        ]
    ]
]);
?>
