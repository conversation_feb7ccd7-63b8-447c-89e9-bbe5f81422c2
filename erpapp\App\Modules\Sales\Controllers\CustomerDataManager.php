<?php
namespace App\Modules\Sales\Controllers;

use PDO;
use Exception;

/**
 * CustomerDataManager - إدارة بيانات العملاء
 * 
 * يحتوي على دوال إدارة العناوين والحسابات البنكية وفحص التغييرات
 * تم التحويل من الموردين إلى العملاء
 */
class CustomerDataManager
{
    /**
     * النموذج للوصول لقاعدة البيانات
     */
    protected $model;
    
    /**
     * Constructor
     */
    public function __construct($model)
    {
        $this->model = $model;
    }
    
    /**
     * فحص ما إذا كانت البيانات تغيرت
     */
    public function hasDataChanged($originalData, $newData, $postData = null)
    {
        // الحقول التي نريد مقارنتها
        $fieldsToCompare = CustomerConfig::getAllFields();
        
        foreach ($fieldsToCompare as $field) {
            $originalValue = $originalData[$field] ?? '';
            $newValue = $newData[$field] ?? '';
            
            // تنظيف القيم للمقارنة
            $cleanOriginal = trim((string)$originalValue);
            $cleanNew = trim((string)$newValue);
            
            // معالجة القيم الفارغة والـ null
            if ($cleanOriginal === '' && $cleanNew === '') {
                continue;
            }
            
            if ($cleanOriginal === '' && $cleanNew === '0') {
                continue;
            }
            
            if ($cleanOriginal === '0' && $cleanNew === '') {
                continue;
            }
            
            // مقارنة القيم
            if ($cleanOriginal !== $cleanNew) {
                return true; // يوجد تغيير
            }
        }
        
        // فحص العناوين والحسابات البنكية إذا تم تمرير POST data
        if ($postData) {
            // فحص العناوين
            if ($this->hasAddressesChanged($originalData, $postData)) {
                return true;
            }
            
            // فحص الحسابات البنكية
            if ($this->hasBankAccountsChanged($originalData, $postData)) {
                return true;
            }
        }
        
        return false; // لا توجد تغييرات
    }
    
    /**
     * فحص تغييرات العناوين
     */
    protected function hasAddressesChanged($originalData, $postData)
    {
        // الحصول على العناوين الحالية من قاعدة البيانات
        $currentAddresses = $this->getCurrentAddresses($originalData['id'] ?? 0);
        
        // الحصول على العناوين الجديدة من POST
        $newAddresses = $postData['addresses'] ?? [];
        
        // مقارنة عدد العناوين
        if (count($currentAddresses) !== count($newAddresses)) {
            return true;
        }
        
        // مقارنة تفصيلية للعناوين
        foreach ($currentAddresses as $index => $currentAddress) {
            $newAddress = $newAddresses[$index] ?? [];
            
            $addressFields = ['address_type', 'address_label', 'address_line1', 'city', 'state_province', 'postal_code', 'country', 'phone'];
            
            foreach ($addressFields as $field) {
                $currentValue = trim($currentAddress[$field] ?? '');
                $newValue = trim($newAddress[$field] ?? '');
                
                if ($currentValue !== $newValue) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * فحص تغييرات الحسابات البنكية
     */
    protected function hasBankAccountsChanged($originalData, $postData)
    {
        // الحصول على الحسابات البنكية الحالية من قاعدة البيانات
        $currentBankAccounts = $this->getCurrentBankAccounts($originalData['id'] ?? 0);
        
        // الحصول على الحسابات البنكية الجديدة من POST
        $newBankAccounts = $postData['bank_accounts'] ?? [];
        
        // مقارنة عدد الحسابات
        if (count($currentBankAccounts) !== count($newBankAccounts)) {
            return true;
        }
        
        // مقارنة تفصيلية للحسابات البنكية
        foreach ($currentBankAccounts as $index => $currentAccount) {
            $newAccount = $newBankAccounts[$index] ?? [];
            
            $accountFields = ['bank_name', 'account_number', 'account_name', 'iban', 'currency'];
            
            foreach ($accountFields as $field) {
                $currentValue = trim($currentAccount[$field] ?? '');
                $newValue = trim($newAccount[$field] ?? '');
                
                if ($currentValue !== $newValue) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * الحصول على العناوين الحالية
     */
    protected function getCurrentAddresses($entityId)
    {
        if (!$entityId) return [];
        
        $sql = "SELECT address_type, address_label, address_line1, city, state_province,
                       postal_code, country, phone, is_default
                FROM entity_addresses
                WHERE entity_id = ?
                ORDER BY id";
        
        $db = $this->model->getDb();
        $stmt = $db->prepare($sql);
        $stmt->execute([$entityId]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على الحسابات البنكية الحالية
     */
    protected function getCurrentBankAccounts($entityId)
    {
        if (!$entityId) return [];
        
        $sql = "SELECT bank_name, account_number, account_name, iban, currency, is_default
                FROM entity_bank_accounts
                WHERE entity_id = ?
                ORDER BY id";
        
        $db = $this->model->getDb();
        $stmt = $db->prepare($sql);
        $stmt->execute([$entityId]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * تحديث عناوين الكيان
     */
    public function updateAddresses($entity_id, $company_id, $postData, $user_id)
    {
        // حذف العناوين الحالية
        $deleteSql = "DELETE FROM entity_addresses WHERE entity_id = ? AND company_id = ?";
        $db = $this->model->getDb();
        $deleteStmt = $db->prepare($deleteSql);
        $deleteStmt->execute([$entity_id, $company_id]);
        
        // إضافة العناوين الجديدة
        if (!empty($postData['addresses'])) {
            $addresses = $postData['addresses'];
            $default_index = $postData['default_address'] ?? 0;
            $this->saveAddresses($entity_id, $company_id, $addresses, $default_index, $user_id);
        }
    }
    
    /**
     * تحديث الحسابات البنكية للكيان
     */
    public function updateBankAccounts($entity_id, $company_id, $postData, $user_id)
    {
        // حذف الحسابات البنكية الحالية
        $deleteSql = "DELETE FROM entity_bank_accounts WHERE entity_id = ? AND company_id = ?";
        $db = $this->model->getDb();
        $deleteStmt = $db->prepare($deleteSql);
        $deleteStmt->execute([$entity_id, $company_id]);
        
        // إضافة الحسابات البنكية الجديدة
        if (!empty($postData['bank_accounts'])) {
            $bank_accounts = $postData['bank_accounts'];
            $default_index = $postData['default_bank_account'] ?? 0;
            $this->saveBankAccounts($entity_id, $company_id, $bank_accounts, $default_index, $user_id);
        }
    }
    
    /**
     * حفظ عناوين الكيان
     */
    public function saveAddresses($entity_id, $company_id, $addresses, $default_index, $user_id)
    {
        $sql = "INSERT INTO entity_addresses (
                    entity_id, company_id, address_type, address_label, address_line1,
                    city, state_province, postal_code, country, phone, is_default,
                    created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $db = $this->model->getDb();
        $stmt = $db->prepare($sql);
        
        foreach ($addresses as $index => $address) {
            if (empty($address['address_type']) || empty($address['address_label']) || empty($address['address_line1'])) {
                continue; // تخطي العناوين غير المكتملة
            }
            
            $is_default = ($index == $default_index) ? 1 : 0;
            
            $stmt->execute([
                $entity_id,
                $company_id,
                $address['address_type'],
                $address['address_label'],
                $address['address_line1'],
                $address['city'],
                $address['state_province'] ?? null,
                $address['postal_code'] ?? null,
                $address['country'] ?? 'Saudi Arabia',
                $address['phone'] ?? null,
                $is_default,
                $user_id
            ]);
        }
    }
    
    /**
     * حفظ الحسابات البنكية للكيان
     */
    public function saveBankAccounts($entity_id, $company_id, $bank_accounts, $default_index, $user_id)
    {
        $sql = "INSERT INTO entity_bank_accounts (
                    entity_id, company_id, bank_name, account_number, account_name,
                    iban, currency, is_default, created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $db = $this->model->getDb();
        $stmt = $db->prepare($sql);
        
        foreach ($bank_accounts as $index => $account) {
            if (empty($account['bank_name']) || empty($account['account_number']) || empty($account['account_name'])) {
                continue; // تخطي الحسابات غير المكتملة
            }
            
            $is_default = ($index == $default_index) ? 1 : 0;
            
            $stmt->execute([
                $entity_id,
                $company_id,
                $account['bank_name'],
                $account['account_number'],
                $account['account_name'],
                $account['iban'] ?? null,
                $account['currency'] ?? 'SAR',
                $is_default,
                $user_id
            ]);
        }
    }
    
    /**
     * إعداد بيانات الصفحة للعرض أو التعديل
     */
    public function prepareViewData($entity_number, $action = 'view')
    {
        $company_id = current_user()['current_company_id'];
        
        // الحصول على بيانات الكيان مع العلاقات
        $entity = $this->model->getWithRelations($entity_number, $company_id);
        
        if (!$entity) {
            return null;
        }
        
        $config = CustomerConfig::getPageTitles();
        $entityName = CustomerConfig::ENTITY_NAME;
        $actionName = $action === 'show' ? 'عرض' : 'تعديل';
        
        return [
            'title' => str_replace('{name}', $entity['G_name_ar'], $config[$action]),
            'entity' => $entity,
            'action' => $action
        ];
    }
    
    /**
     * إعداد بيانات صفحة الإنشاء
     */
    public function prepareCreateData($groupModel)
    {
        $company_id = current_user()['current_company_id'];
        
        // الحصول على مجموعات الكيان
        $groups = $groupModel->getForSelect($company_id);
        
        // الحصول على المجموعة الافتراضية
        $defaultGroup = $groupModel->getDefaultGroup($company_id);
        
        $config = CustomerConfig::getPageTitles();
        
        return [
            'title' => $config['create'],
            'groups' => $groups,
            'defaultGroup' => $defaultGroup
        ];
    }
}
