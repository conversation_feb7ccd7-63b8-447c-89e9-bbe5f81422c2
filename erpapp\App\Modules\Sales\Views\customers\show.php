<?php
// تضمين القالب المشترك للحصول على $form_tabs
include __DIR__ . '/form_template.php';

// إعداد الإجراءات للعرض فقط
$actions = [
    [
        'type' => 'secondary',
        'url' => 'sales/customers',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'primary',
        'url' => 'sales/customers/' . ($customer['entity_number'] ?? '') . '/edit',
        'icon' => 'fas fa-edit',
        'text' => 'تعديل العميل'
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المبيعات', 'url' => 'sales'],
    ['title' => 'العملاء', 'url' => 'sales/customers'],
    ['title' => $customer['G_name_ar'] ?? 'عرض العميل', 'active' => true]
];

// استخدام النظام الموحد في وضع القراءة فقط
render_form_page([
    'title' => $title ?? 'عرض العميل',
    'module' => 'sales',
    'entity' => 'customers',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    'form_data' => $customer ?? [], // بيانات العميل الحالية
    'readonly' => true, // وضع القراءة فقط
    // النموذج بالتبويبات الديناميكية
    'form' => [
        'action' => '#', // لا يوجد action في وضع العرض
        'method' => 'GET',
        'id' => 'customerForm',
        'tabs' => $form_tabs
    ]
]);
?>
