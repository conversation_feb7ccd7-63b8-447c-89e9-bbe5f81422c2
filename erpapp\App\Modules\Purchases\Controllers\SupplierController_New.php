<?php
namespace App\Modules\Purchases\Controllers;

use App\Modules\Purchases\Models\Supplier;
use App\Modules\Purchases\Models\SupplierGroup;
use Exception;

/**
 * SupplierController - متحكم الموردين (مبسط ومقسم)
 * 
 * هذا هو المتحكم الرئيسي المبسط الذي يستخدم الملفات المنفصلة
 * يمكن نسخ هذا الملف وتعديله بسهولة للعملاء
 */
class SupplierController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Supplier model
     */
    protected $supplierModel;

    /**
     * SupplierGroup model
     */
    protected $supplierGroupModel;
    
    /**
     * Validation helper
     */
    protected $validation;
    
    /**
     * Data manager helper
     */
    protected $dataManager;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->supplierModel = new Supplier();
        $this->supplierGroupModel = new SupplierGroup();
        $this->validation = new SupplierValidation($this->supplierModel);
        $this->dataManager = new SupplierDataManager($this->supplierModel);

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash('supplier_error', 'يجب تحديد شركة حالية للوصول إلى الموردين', 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * عرض قائمة الموردين
     */
    public function index()
    {
        $config = SupplierConfig::getUrlConfig();
        
        // استخدام الدالة الموحدة لمعالجة index
        $data = handle_datatable_index([
            'filter_name' => SupplierConfig::FILTER_NAME,
            'default_filters' => SupplierConfig::getDefaultFilters(),
            'filter_fields' => SupplierConfig::getFilterFields(),
            'model' => $this->supplierModel,
            'title' => SupplierConfig::getPageTitles()['index'],
            'data_key' => SupplierConfig::VIEW_ENTITIES_VAR,
            'breadcrumb' => SupplierBreadcrumbs::getIndexBreadcrumb(),
            'additional_data' => [
                SupplierConfig::VIEW_GROUPS_VAR => $this->supplierGroupModel->getForSelect(current_user()['current_company_id'])
            ]
        ]);

        view('Purchases::suppliers/index', $data);
    }

    /**
     * تطبيق فلاتر الموردين
     */
    public function applyFilters()
    {
        $config = SupplierConfig::getUrlConfig();
        handle_apply_filters(
            SupplierConfig::FILTER_NAME, 
            SupplierConfig::getFilterFields(), 
            base_url($config['index'])
        );
    }

    /**
     * مسح فلاتر الموردين
     */
    public function clearFilters()
    {
        $config = SupplierConfig::getUrlConfig();
        handle_clear_filters(
            SupplierConfig::FILTER_NAME, 
            base_url($config['index'])
        );
    }

    /**
     * عرض إحصائيات الموردين
     */
    public function stats()
    {
        $company_id = current_user()['current_company_id'];
        $stats = $this->supplierModel->getStats($company_id);

        $data = [
            'title' => SupplierConfig::getPageTitles()['stats'],
            'stats' => $stats,
            'breadcrumb' => SupplierBreadcrumbs::getStatsBreadcrumb()
        ];

        view('Purchases::suppliers/stats', $data);
    }

    /**
     * عرض مختلط للموردين
     */
    public function mixed()
    {
        // استخدام الدالة الموحدة لمعالجة index (للجدول)
        $data = handle_datatable_index([
            'filter_name' => SupplierConfig::FILTER_NAME,
            'default_filters' => SupplierConfig::getDefaultFilters(),
            'filter_fields' => SupplierConfig::getFilterFields(),
            'model' => $this->supplierModel,
            'title' => SupplierConfig::getPageTitles()['mixed'],
            'data_key' => SupplierConfig::VIEW_ENTITIES_VAR,
            'breadcrumb' => SupplierBreadcrumbs::getMixedBreadcrumb(),
            'additional_data' => [
                SupplierConfig::VIEW_GROUPS_VAR => $this->supplierGroupModel->getForSelect(current_user()['current_company_id'])
            ]
        ]);

        // إضافة الإحصائيات
        $company_id = current_user()['current_company_id'];
        $data['stats'] = $this->supplierModel->getStats($company_id);

        view('Purchases::suppliers/mixed', $data);
    }

    /**
     * عرض صفحة إضافة مورد جديد
     */
    public function create()
    {
        $data = $this->dataManager->prepareCreateData($this->supplierGroupModel);
        $data['breadcrumb'] = SupplierBreadcrumbs::getCreateBreadcrumb();

        // إضافة المتغيرات المطلوبة للـ view
        $data['supplierGroups'] = $data['groups'];
        unset($data['groups']);

        view('Purchases::suppliers/create', $data);
    }

    /**
     * حفظ مورد جديد
     */
    public function store()
    {
        try {
            $company_id = current_user()['current_company_id'];
            $user_id = current_user()['UserID'];

            // بدء المعاملة
            $this->supplierModel->beginTransaction();

            // التحقق من صحة البيانات
            $validatedData = $this->validation->validateSupplierData($_POST);
            $validatedData['company_id'] = $company_id;
            $validatedData['created_by'] = $user_id;

            // إنشاء المورد
            $supplierNumber = $this->supplierModel->create($validatedData);

            if ($supplierNumber) {
                // الحصول على entity_id للمورد الجديد
                $supplier = $this->supplierModel->getByNumber($supplierNumber, $company_id);
                $entity_id = $supplier['id'];

                // حفظ العناوين
                if (!empty($_POST['addresses'])) {
                    $this->dataManager->saveAddresses($entity_id, $company_id, $_POST['addresses'], $_POST['default_address'] ?? 0, $user_id);
                }

                // حفظ الحسابات البنكية
                if (!empty($_POST['bank_accounts'])) {
                    $this->dataManager->saveBankAccounts($entity_id, $company_id, $_POST['bank_accounts'], $_POST['default_bank_account'] ?? 0, $user_id);
                }

                // تأكيد المعاملة
                $this->supplierModel->commit();

                $messages = SupplierConfig::getSuccessMessages();
                flash('success', $messages['created']);
                
                $config = SupplierConfig::getUrlConfig();
                redirect(base_url($config['index']));
            } else {
                $this->supplierModel->rollback();
                $messages = SupplierConfig::getErrorMessages();
                flash('error', $messages['create_failed']);
                
                $config = SupplierConfig::getUrlConfig();
                redirect(base_url($config['create']));
            }

        } catch (Exception $e) {
            $this->supplierModel->rollback();
            flash('error', $e->getMessage());
            
            $config = SupplierConfig::getUrlConfig();
            redirect(base_url($config['create']));
        }
    }

    /**
     * عرض تفاصيل مورد
     */
    public function show()
    {
        $entity_number = $this->params['id'];
        $data = $this->dataManager->prepareViewData($entity_number, 'view');

        if (!$data) {
            $messages = SupplierConfig::getErrorMessages();
            flash('error', $messages['not_found']);
            
            $config = SupplierConfig::getUrlConfig();
            redirect(base_url($config['index']));
        }

        // إعداد البيانات للـ view (توافق مع Views الحالية)
        $data['supplier'] = $data['entity'];
        $data['supplierGroups'] = $this->supplierGroupModel->getForSelect(current_user()['current_company_id']);
        $data['breadcrumb'] = SupplierBreadcrumbs::getShowBreadcrumb($data['entity']['G_name_ar']);
        unset($data['entity']);

        view('Purchases::suppliers/show', $data);
    }

    /**
     * عرض صفحة تعديل مورد
     */
    public function edit()
    {
        $entity_number = $this->params['id'];
        $data = $this->dataManager->prepareViewData($entity_number, 'edit');

        if (!$data) {
            $messages = SupplierConfig::getErrorMessages();
            flash('error', $messages['not_found']);
            
            $config = SupplierConfig::getUrlConfig();
            redirect(base_url($config['index']));
        }

        // إعداد البيانات للـ view (توافق مع Views الحالية)
        $data['supplier'] = $data['entity'];
        $data['supplierGroups'] = $this->supplierGroupModel->getForSelect(current_user()['current_company_id']);
        $data['breadcrumb'] = SupplierBreadcrumbs::getEditBreadcrumb($data['entity']['G_name_ar']);
        unset($data['entity']);

        view('Purchases::suppliers/edit', $data);
    }

    /**
     * تحديث مورد
     */
    public function update()
    {
        try {
            $entity_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المورد
            $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);
            if (!$supplier) {
                $messages = SupplierConfig::getErrorMessages();
                flash('error', $messages['not_found']);
                
                $config = SupplierConfig::getUrlConfig();
                redirect(base_url($config['index']));
            }

            // التحقق من صحة البيانات
            $validatedData = $this->validation->validateSupplierData($_POST, $entity_number);

            // فحص التغييرات قبل التحديث
            if (!$this->dataManager->hasDataChanged($supplier, $validatedData, $_POST)) {
                $messages = SupplierConfig::getErrorMessages();
                flash('info', $messages['no_changes']);
                
                $config = SupplierConfig::getUrlConfig();
                redirect(base_url(str_replace('{id}', $entity_number, $config['edit'])));
                return;
            }

            $validatedData['updated_by'] = current_user()['UserID'];

            // بدء المعاملة
            $db = $this->supplierModel->getDb();
            $db->beginTransaction();

            try {
                // تحديث البيانات الأساسية للمورد
                $result = $this->supplierModel->update($entity_number, $validatedData, $company_id);

                if (!$result) {
                    $messages = SupplierConfig::getErrorMessages();
                    throw new Exception($messages['update_failed']);
                }

                // الحصول على entity_id
                $entity_id = $supplier['id'];
                $user_id = current_user()['UserID'];

                // تحديث العناوين
                $this->dataManager->updateAddresses($entity_id, $company_id, $_POST, $user_id);

                // تحديث الحسابات البنكية
                $this->dataManager->updateBankAccounts($entity_id, $company_id, $_POST, $user_id);

                // تأكيد المعاملة
                $db->commit();

                $messages = SupplierConfig::getSuccessMessages();
                flash('success', str_replace('{number}', $entity_number, $messages['updated']));
                
                $config = SupplierConfig::getUrlConfig();
                redirect(base_url($config['index']));

            } catch (Exception $e) {
                // إلغاء المعاملة
                $db->rollBack();
                throw $e;
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            
            $config = SupplierConfig::getUrlConfig();
            redirect(base_url(str_replace('{id}', $this->params['id'], $config['edit'])));
        }
    }

    /**
     * حذف مورد
     */
    public function delete()
    {
        try {
            $entity_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المورد
            $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);
            if (!$supplier) {
                $messages = SupplierConfig::getErrorMessages();
                flash('error', $messages['not_found']);
                
                $config = SupplierConfig::getUrlConfig();
                redirect(base_url($config['index']));
            }

            // حذف المورد
            $result = $this->supplierModel->delete($entity_number, $company_id);

            if ($result) {
                $messages = SupplierConfig::getSuccessMessages();
                flash('success', $messages['deleted']);
            } else {
                $messages = SupplierConfig::getErrorMessages();
                flash('error', $messages['delete_failed']);
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
        }

        $config = SupplierConfig::getUrlConfig();
        redirect(base_url($config['index']));
    }
}
