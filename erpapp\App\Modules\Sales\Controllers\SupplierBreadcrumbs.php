<?php
namespace App\Modules\Purchases\Controllers;

/**
 * SupplierBreadcrumbs - إدارة breadcrumbs الموردين
 * 
 * يحتوي على دوال إنشاء breadcrumbs للصفحات المختلفة
 * يمكن نسخ هذا الملف وتعديله للعملاء
 */
class SupplierBreadcrumbs
{
    /**
     * إعداد breadcrumb للصفحات
     */
    public static function prepareBreadcrumb($action, $entityName = null)
    {
        $config = SupplierConfig::getBreadcrumbConfig();
        
        $breadcrumb = [
            ['title' => $config['module_title'], 'url' => base_url($config['module_url'])],
            ['title' => $config['entity_title'], 'url' => base_url($config['entity_url'])]
        ];
        
        switch ($action) {
            case 'index':
                $breadcrumb[count($breadcrumb) - 1]['active'] = true;
                break;
                
            case 'create':
                $breadcrumb[] = ['title' => 'إضافة ' . SupplierConfig::ENTITY_NAME, 'active' => true];
                break;

            case 'edit':
                $actionTitle = 'تعديل ' . SupplierConfig::ENTITY_NAME;
                if ($entityName) {
                    $actionTitle .= ' - ' . $entityName;
                }
                $breadcrumb[] = ['title' => $actionTitle, 'active' => true];
                break;

            case 'view':
            case 'show':
                $actionTitle = 'عرض ' . SupplierConfig::ENTITY_NAME;
                if ($entityName) {
                    $actionTitle .= ' - ' . $entityName;
                }
                $breadcrumb[] = ['title' => $actionTitle, 'active' => true];
                break;
                
            case 'stats':
                $breadcrumb[] = ['title' => 'الإحصائيات', 'active' => true];
                break;
                
            case 'mixed':
                $breadcrumb[] = ['title' => 'عرض مختلط', 'active' => true];
                break;
                
            default:
                $breadcrumb[count($breadcrumb) - 1]['active'] = true;
        }
        
        return $breadcrumb;
    }
    
    /**
     * إعداد breadcrumb لصفحة القائمة
     */
    public static function getIndexBreadcrumb()
    {
        return self::prepareBreadcrumb('index');
    }
    
    /**
     * إعداد breadcrumb لصفحة الإنشاء
     */
    public static function getCreateBreadcrumb()
    {
        return self::prepareBreadcrumb('create');
    }
    
    /**
     * إعداد breadcrumb لصفحة العرض
     */
    public static function getShowBreadcrumb($entityName = null)
    {
        return self::prepareBreadcrumb('show', $entityName);
    }
    
    /**
     * إعداد breadcrumb لصفحة التعديل
     */
    public static function getEditBreadcrumb($entityName = null)
    {
        return self::prepareBreadcrumb('edit', $entityName);
    }
    
    /**
     * إعداد breadcrumb لصفحة الإحصائيات
     */
    public static function getStatsBreadcrumb()
    {
        return self::prepareBreadcrumb('stats');
    }
    
    /**
     * إعداد breadcrumb لصفحة العرض المختلط
     */
    public static function getMixedBreadcrumb()
    {
        return self::prepareBreadcrumb('mixed');
    }
}
