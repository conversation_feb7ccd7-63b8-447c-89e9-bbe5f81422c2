<?php
namespace App\Modules\Purchases\Controllers;

/**
 * SupplierConfig - إعدادات وحقول الموردين
 * 
 * يحتوي على جميع الإعدادات والحقول الخاصة بالموردين
 * يمكن نسخ هذا الملف وتعديله للعملاء
 */
class SupplierConfig
{
    /**
     * اسم الكيان (للعرض)
     */
    const ENTITY_NAME = 'المورد';
    const ENTITY_NAME_PLURAL = 'الموردين';
    
    /**
     * اسم الكيان (للكود)
     */
    const ENTITY_CODE = 'supplier';
    const ENTITY_CODE_PLURAL = 'suppliers';
    
    /**
     * اسم الوحدة
     */
    const MODULE_NAME = 'المشتريات';
    const MODULE_CODE = 'purchases';
    
    /**
     * مسارات الوحدة
     */
    const MODULE_URL = 'purchases';
    const ENTITY_URL = 'purchases/suppliers';
    
    /**
     * أسماء النماذج
     */
    const MODEL_CLASS = 'App\Modules\Purchases\Models\Supplier';
    const GROUP_MODEL_CLASS = 'App\Modules\Purchases\Models\SupplierGroup';
    
    /**
     * أسماء المتغيرات في الـ views
     */
    const VIEW_ENTITY_VAR = 'supplier';
    const VIEW_ENTITIES_VAR = 'suppliers';
    const VIEW_GROUPS_VAR = 'supplierGroups';
    
    /**
     * أسماء الفلاتر
     */
    const FILTER_NAME = 'suppliers';
    
    /**
     * الحقول المشتركة بين جميع الكيانات
     */
    public static function getCommonFields()
    {
        return [
            'group_id', 'G_name_ar', 'G_name_en', 'G_phone', 'G_mobile',
            'G_website', 'G_notes', 'G_status'
        ];
    }
    
    /**
     * الحقول الخاصة بالموردين
     */
    public static function getSpecificFields()
    {
        return [
            'S_company_name', 'S_contact_person', 'S_email', 'S_tax_number', 
            'S_commercial_register', 'S_payment_terms', 'S_credit_limit', 
            'S_discount_rate', 'S_delivery_time', 'S_minimum_order',
            'S_currency', 'S_rating', 'S_license_number', 'S_license_expiry',
            'S_establishment_date', 'S_legal_form', 'S_internal_notes', 'S_special_instructions'
        ];
    }
    
    /**
     * جميع الحقول للمقارنة
     */
    public static function getAllFields()
    {
        return array_merge(self::getCommonFields(), self::getSpecificFields());
    }
    
    /**
     * الحقول المطلوبة
     */
    public static function getRequiredFields()
    {
        return [
            'G_name_ar' => 'اسم ' . self::ENTITY_NAME . ' بالعربية مطلوب'
        ];
    }
    
    /**
     * حقول البريد الإلكتروني للتحقق
     */
    public static function getEmailFields()
    {
        return [
            'S_email' => 'البريد الإلكتروني غير صحيح'
        ];
    }
    
    /**
     * حقول المواقع الإلكترونية للتحقق
     */
    public static function getWebsiteFields()
    {
        return [
            'G_website' => 'الموقع الإلكتروني غير صحيح'
        ];
    }
    
    /**
     * الحقول الرقمية للتحقق
     */
    public static function getNumericFields()
    {
        return [
            'S_payment_terms' => [
                'default' => 30,
                'min' => 0,
                'error' => 'شروط الدفع يجب أن تكون رقماً موجباً'
            ],
            'S_credit_limit' => [
                'default' => 0,
                'min' => 0,
                'error' => 'الحد الائتماني يجب أن يكون رقماً موجباً'
            ],
            'S_discount_rate' => [
                'default' => 0,
                'min' => 0,
                'max' => 100,
                'error' => 'معدل الخصم يجب أن يكون رقماً بين 0 و 100'
            ],
            'S_delivery_time' => [
                'default' => null,
                'min' => 0,
                'error' => 'مدة التسليم يجب أن تكون رقماً موجباً'
            ],
            'S_minimum_order' => [
                'default' => null,
                'min' => 0,
                'error' => 'الحد الأدنى للطلب يجب أن يكون رقماً موجباً'
            ]
        ];
    }
    
    /**
     * القيم الافتراضية للحقول
     */
    public static function getDefaultValues()
    {
        return [
            'G_status' => 'active',
            'S_payment_terms' => 30,
            'S_credit_limit' => 0,
            'S_discount_rate' => 0,
            'S_currency' => 'SAR',
            'S_rating' => 'C'
        ];
    }
    
    /**
     * خيارات الحالة
     */
    public static function getStatusOptions()
    {
        return [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ];
    }
    
    /**
     * خيارات التقييم
     */
    public static function getRatingOptions()
    {
        return [
            'A' => 'ممتاز',
            'B' => 'جيد جداً',
            'C' => 'جيد',
            'D' => 'مقبول'
        ];
    }
    
    /**
     * خيارات العملة
     */
    public static function getCurrencyOptions()
    {
        return [
            'SAR' => 'ريال سعودي',
            'USD' => 'دولار أمريكي',
            'EUR' => 'يورو',
            'GBP' => 'جنيه إسترليني'
        ];
    }
    
    /**
     * خيارات الشكل القانوني
     */
    public static function getLegalFormOptions()
    {
        return [
            'individual' => 'فردي',
            'llc' => 'شركة ذات مسؤولية محدودة',
            'corporation' => 'شركة مساهمة',
            'partnership' => 'شراكة',
            'other' => 'أخرى'
        ];
    }
    
    /**
     * إعدادات الفلاتر الافتراضية
     */
    public static function getDefaultFilters()
    {
        return [
            'status' => '',
            'group_id' => ''
        ];
    }
    
    /**
     * حقول الفلاتر
     */
    public static function getFilterFields()
    {
        return ['search', 'status', 'group_id'];
    }
    
    /**
     * رسائل النجاح
     */
    public static function getSuccessMessages()
    {
        return [
            'created' => 'تم إنشاء ' . self::ENTITY_NAME . ' بنجاح مع جميع البيانات',
            'updated' => 'تم تحديث ' . self::ENTITY_NAME . ' بنجاح - رقم ' . self::ENTITY_NAME . ': {number}',
            'deleted' => 'تم حذف ' . self::ENTITY_NAME . ' بنجاح'
        ];
    }
    
    /**
     * رسائل الخطأ
     */
    public static function getErrorMessages()
    {
        return [
            'not_found' => self::ENTITY_NAME . ' غير موجود',
            'create_failed' => 'حدث خطأ أثناء إنشاء ' . self::ENTITY_NAME,
            'update_failed' => 'فشل في تحديث البيانات الأساسية لـ' . self::ENTITY_NAME,
            'delete_failed' => 'حدث خطأ أثناء حذف ' . self::ENTITY_NAME,
            'no_changes' => 'لا توجد تغييرات لحفظها',
            'duplicate_name' => 'اسم ' . self::ENTITY_NAME . ' موجود بالفعل في الشركة الحالية'
        ];
    }
    
    /**
     * عناوين الصفحات
     */
    public static function getPageTitles()
    {
        return [
            'index' => self::ENTITY_NAME_PLURAL,
            'create' => 'إضافة ' . self::ENTITY_NAME . ' جديد',
            'show' => 'عرض ' . self::ENTITY_NAME . ' - {name}',
            'edit' => 'تعديل ' . self::ENTITY_NAME . ' - {name}',
            'stats' => 'إحصائيات ' . self::ENTITY_NAME_PLURAL,
            'mixed' => self::ENTITY_NAME_PLURAL . ' - عرض مختلط'
        ];
    }
    
    /**
     * إعدادات الـ breadcrumbs
     */
    public static function getBreadcrumbConfig()
    {
        return [
            'module_title' => self::MODULE_NAME,
            'module_url' => self::MODULE_URL,
            'entity_title' => self::ENTITY_NAME_PLURAL,
            'entity_url' => self::ENTITY_URL
        ];
    }
    
    /**
     * إعدادات الـ views (للمرجع فقط - يتم استخدام أسماء ثابتة للتوافق)
     */
    public static function getViewConfig()
    {
        return [
            'namespace' => 'Purchases',
            'folder' => 'suppliers',
            'entity_var' => 'supplier',      // ثابت للتوافق مع Views
            'entities_var' => 'suppliers',   // ثابت للتوافق مع Views
            'groups_var' => 'supplierGroups' // ثابت للتوافق مع Views
        ];
    }
    
    /**
     * إعدادات الـ URLs
     */
    public static function getUrlConfig()
    {
        return [
            'index' => self::ENTITY_URL,
            'create' => self::ENTITY_URL . '/create',
            'store' => self::ENTITY_URL . '/store',
            'show' => self::ENTITY_URL . '/{id}',
            'edit' => self::ENTITY_URL . '/{id}/edit',
            'update' => self::ENTITY_URL . '/{id}/update',
            'delete' => self::ENTITY_URL . '/{id}/delete',
            'stats' => self::ENTITY_URL . '/stats',
            'mixed' => self::ENTITY_URL . '/mixed',
            'apply_filters' => self::ENTITY_URL . '/apply-filters',
            'clear_filters' => self::ENTITY_URL . '/clear-filters'
        ];
    }
}
