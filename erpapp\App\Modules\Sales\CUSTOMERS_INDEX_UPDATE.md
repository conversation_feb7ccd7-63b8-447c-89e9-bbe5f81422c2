# 🔄 **تحديث ملف index.php للعملاء**

## ✅ **تم التحديث بنجاح!**

تم تحديث ملف `index.php` للعملاء ليكون **مطابق تمام所有情节** لملف `index.php` للموردين، ولكن مع حقول العملاء من `tables.sql`.

---

## 📊 **الحقول المعروضة في الجدول:**

### **الحقول من جدول `concerned_entities`:**

#### **1. الحقول الأساسية:**
- ✅ `entity_number` - رقم العميل
- ✅ `G_name_ar` - اسم العميل بالعربية (مع رابط للعرض)
- ✅ `G_name_en` - اسم العميل بالإنجليزية (كـ subtitle)
- ✅ `G_phone` - رقم الهاتف
- ✅ `G_status` - حالة العميل (نشط/غير نشط/معلق)

#### **2. الحقول الخاصة بالعملاء (بادئة C_):**
- ✅ `C_customer_type` - نوع العميل (فردي/شركة)
- ✅ `C_email` - البريد الإلكتروني
- ✅ `C_credit_limit` - الحد الائتماني
- ✅ `C_rating` - تقييم العميل (A/B/C/D)

#### **3. الحقول المرتبطة:**
- ✅ `group_name` - اسم مجموعة العملاء

---

## 🎨 **تصميم الأعمدة:**

### **1. عمود رقم العميل:**
```php
[
    'field' => 'entity_number',
    'title' => 'الرقم',
    'type' => 'text',
    'width' => '120px',
    'sortable' => true,
    'data_type' => 'number'
]
```

### **2. عمود اسم العميل (مع رابط):**
```php
[
    'field' => 'G_name_ar',
    'title' => 'اسم العميل',
    'type' => 'link',
    'url' => 'sales/customers/{entity_number}',
    'subtitle_field' => 'G_name_en',
    'sortable' => true,
    'data_type' => 'text'
]
```

### **3. عمود نوع العميل (مع ألوان):**
```php
[
    'field' => 'C_customer_type',
    'title' => 'نوع العميل',
    'type' => 'badge',
    'status_config' => [
        'classes' => [
            'individual' => 'primary',  // أزرق للفردي
            'company' => 'success'      // أخضر للشركة
        ],
        'texts' => [
            'individual' => 'فردي',
            'company' => 'شركة'
        ]
    ]
]
```

### **4. عمود التقييم (مع ألوان متدرجة):**
```php
[
    'field' => 'C_rating',
    'title' => 'التقييم',
    'type' => 'badge',
    'status_config' => [
        'classes' => [
            'A' => 'success',   // أخضر للممتاز
            'B' => 'info',      // أزرق للجيد
            'C' => 'warning',   // أصفر للمتوسط
            'D' => 'danger'     // أحمر للضعيف
        ],
        'texts' => [
            'A' => 'ممتاز',
            'B' => 'جيد',
            'C' => 'متوسط',
            'D' => 'ضعيف'
        ]
    ]
]
```

### **5. عمود الحد الائتماني (مع تنسيق العملة):**
```php
[
    'field' => 'C_credit_limit',
    'title' => 'الحد الائتماني',
    'type' => 'currency',
    'sortable' => true,
    'data_type' => 'number'
]
```

---

## 🔧 **الإجراءات والأزرار:**

### **أزرار الإجراءات الرئيسية:**
- ✅ **لوحة التحكم** - `sales/customers/dashboard`
- ✅ **عرض الإحصائيات** - `sales/customers/stats`
- ✅ **إضافة عميل جديد** - `sales/customers/create`

### **أزرار CSV:**
- ✅ **تصدير CSV** - `sales/customers/export`
- ✅ **استيراد CSV** - نافذة منبثقة للاستيراد

### **أزرار الصف (لكل عميل):**
- ✅ **عرض** - `sales/customers/{entity_number}`
- ✅ **تعديل** - `sales/customers/{entity_number}/edit`
- ✅ **حذف** - نافذة تأكيد الحذف

---

## 🔍 **الفلاتر:**

### **1. فلتر البحث:**
```php
[
    'name' => 'search',
    'type' => 'search',
    'label' => 'البحث في العملاء',
    'placeholder' => 'ابحث بالاسم أو البريد الإلكتروني...',
    'help' => 'البحث في الاسم العربي، الإنجليزي، أو البريد الإلكتروني'
]
```

### **2. فلتر الحالة:**
```php
[
    'name' => 'status',
    'type' => 'select',
    'label' => 'حالة العميل',
    'options' => [
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'suspended' => 'معلق'
    ]
]
```

### **3. فلتر المجموعة:**
```php
[
    'name' => 'group_id',
    'type' => 'select',
    'label' => 'مجموعة العملاء',
    'options' => array_column($customerGroups ?? [], 'name_ar', 'group_number')
]
```

---

## 🎯 **المقارنة مع الموردين:**

| **الخاصية** | **الموردين** | **العملاء** |
|-------------|-------------|-------------|
| **الحقل الخاص** | `S_company_name` | `C_customer_type` |
| **البريد** | `S_email` | `C_email` |
| **التقييم** | `S_rating` | `C_rating` |
| **الحد المالي** | `S_credit_limit` | `C_credit_limit` |
| **المسار** | `purchases/suppliers` | `sales/customers` |
| **الأيقونة** | `fas fa-truck-loading` | `fas fa-users` |

---

## 📋 **البيانات المطلوبة من Controller:**

### **المتغيرات الأساسية:**
```php
$customers = []; // بيانات العملاء من قاعدة البيانات
$customerGroups = []; // مجموعات العملاء للفلاتر
$pagination = []; // معلومات التصفح
$filters = []; // الفلاتر المطبقة
$title = 'العملاء'; // عنوان الصفحة
```

### **الحقول المطلوبة في كل عميل:**
```php
[
    'entity_number' => 1,
    'G_name_ar' => 'اسم العميل',
    'G_name_en' => 'Customer Name',
    'C_customer_type' => 'individual', // أو 'company'
    'C_email' => '<EMAIL>',
    'group_name' => 'عملاء عامون',
    'G_phone' => '0501234567',
    'C_credit_limit' => 10000.00,
    'C_rating' => 'A', // A, B, C, أو D
    'G_status' => 'active' // active, inactive, أو suspended
]
```

---

## ✅ **النتيجة:**

**تم إنشاء ملف `index.php` للعملاء مطابق تمام所有情节 لملف الموردين!**

### **المميزات:**
- ✅ **نفس الهيكل** والتصميم
- ✅ **حقول العملاء** من `tables.sql`
- ✅ **ألوان مناسبة** لكل نوع بيانات
- ✅ **فلاتر ذكية** للبحث والتصفية
- ✅ **أزرار تفاعلية** لجميع الإجراءات
- ✅ **تصميم متجاوب** مع جميع الشاشات

### **الخطوة التالية:**
تحديث `CustomerController` ليرسل البيانات بالتنسيق المطلوب لهذا الملف.

**الآن ملف index.php للعملاء جاهز ومطابق للموردين!** 🎉
