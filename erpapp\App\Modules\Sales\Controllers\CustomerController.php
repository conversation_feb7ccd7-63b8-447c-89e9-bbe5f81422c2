<?php
namespace App\Modules\Sales\Controllers;

use App\Modules\Sales\Models\Customer;
use App\Modules\Sales\Models\CustomerGroup;
use Exception;

/**
 * CustomerController - متحكم العملاء (مبسط ومقسم)
 * 
 * هذا هو المتحكم الرئيسي المبسط الذي يستخدم الملفات المنفصلة
 * تم التحويل من الموردين إلى العملاء
 */
class CustomerController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Customer model
     */
    protected $customerModel;

    /**
     * CustomerGroup model
     */
    protected $customerGroupModel;
    
    /**
     * Validation helper
     */
    protected $validation;
    
    /**
     * Data manager helper
     */
    protected $dataManager;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->customerModel = new Customer();
        $this->customerGroupModel = new CustomerGroup();
        $this->validation = new CustomerValidation($this->customerModel);
        $this->dataManager = new CustomerDataManager($this->customerModel);

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash('customer_error', 'يجب تحديد شركة حالية للوصول إلى العملاء', 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * عرض قائمة العملاء
     */
    public function index()
    {
        $config = CustomerConfig::getUrlConfig();
        
        // التأكد من وجود مجموعة افتراضية للعملاء
        $this->ensureDefaultCustomerGroup();

        // استخدام الدالة الموحدة لمعالجة index
        $data = handle_datatable_index([
            'filter_name' => CustomerConfig::FILTER_NAME,
            'default_filters' => CustomerConfig::getDefaultFilters(),
            'filter_fields' => CustomerConfig::getFilterFields(),
            'model' => $this->customerModel,
            'title' => CustomerConfig::getPageTitles()['index'],
            'data_key' => CustomerConfig::VIEW_ENTITIES_VAR,
            'breadcrumb' => CustomerBreadcrumbs::getIndexBreadcrumb(),
            'additional_data' => [
                CustomerConfig::VIEW_GROUPS_VAR => $this->customerGroupModel->getForSelect(current_user()['current_company_id'])
            ]
        ]);

        view('Sales::customers/index', $data);
    }

    /**
     * تطبيق فلاتر العملاء
     */
    public function applyFilters()
    {
        $config = CustomerConfig::getUrlConfig();
        handle_apply_filters(
            CustomerConfig::FILTER_NAME, 
            CustomerConfig::getFilterFields(), 
            base_url($config['index'])
        );
    }

    /**
     * مسح فلاتر العملاء
     */
    public function clearFilters()
    {
        $config = CustomerConfig::getUrlConfig();
        handle_clear_filters(
            CustomerConfig::FILTER_NAME, 
            base_url($config['index'])
        );
    }

    /**
     * عرض إحصائيات العملاء
     */
    public function stats()
    {
        $company_id = current_user()['current_company_id'];
        $stats = $this->customerModel->getStats($company_id);

        $data = [
            'title' => CustomerConfig::getPageTitles()['stats'],
            'stats' => $stats,
            'breadcrumb' => CustomerBreadcrumbs::getStatsBreadcrumb()
        ];

        view('Sales::customers/stats', $data);
    }

    /**
     * عرض مختلط للعملاء
     */
    public function mixed()
    {
        // استخدام الدالة الموحدة لمعالجة index (للجدول)
        $data = handle_datatable_index([
            'filter_name' => CustomerConfig::FILTER_NAME,
            'default_filters' => CustomerConfig::getDefaultFilters(),
            'filter_fields' => CustomerConfig::getFilterFields(),
            'model' => $this->customerModel,
            'title' => CustomerConfig::getPageTitles()['mixed'],
            'data_key' => CustomerConfig::VIEW_ENTITIES_VAR,
            'breadcrumb' => CustomerBreadcrumbs::getMixedBreadcrumb(),
            'additional_data' => [
                CustomerConfig::VIEW_GROUPS_VAR => $this->customerGroupModel->getForSelect(current_user()['current_company_id'])
            ]
        ]);

        // إضافة الإحصائيات
        $company_id = current_user()['current_company_id'];
        $data['stats'] = $this->customerModel->getStats($company_id);

        view('Sales::customers/mixed', $data);
    }

    /**
     * عرض صفحة إضافة عميل جديد
     */
    public function create()
    {
        $data = $this->dataManager->prepareCreateData($this->customerGroupModel);
        $data['breadcrumb'] = CustomerBreadcrumbs::getCreateBreadcrumb();
        
        // إضافة المتغيرات المطلوبة للـ view
        $data['customerGroups'] = $data['groups'];
        unset($data['groups']);

        view('Sales::customers/create', $data);
    }

    /**
     * حفظ عميل جديد
     */
    public function store()
    {
        try {
            $company_id = current_user()['current_company_id'];
            $user_id = current_user()['UserID'];

            // بدء المعاملة
            $this->customerModel->beginTransaction();

            // التحقق من صحة البيانات
            $validatedData = $this->validation->validateCustomerData($_POST);
            $validatedData['company_id'] = $company_id;
            $validatedData['created_by'] = $user_id;

            // إنشاء العميل
            $customerNumber = $this->customerModel->create($validatedData);

            if ($customerNumber) {
                // الحصول على entity_id للعميل الجديد
                $customer = $this->customerModel->getByNumber($customerNumber, $company_id);
                $entity_id = $customer['id'];

                // حفظ العناوين
                if (!empty($_POST['addresses'])) {
                    $this->dataManager->saveAddresses($entity_id, $company_id, $_POST['addresses'], $_POST['default_address'] ?? 0, $user_id);
                }

                // حفظ الحسابات البنكية
                if (!empty($_POST['bank_accounts'])) {
                    $this->dataManager->saveBankAccounts($entity_id, $company_id, $_POST['bank_accounts'], $_POST['default_bank_account'] ?? 0, $user_id);
                }

                // تأكيد المعاملة
                $this->customerModel->commit();

                $messages = CustomerConfig::getSuccessMessages();
                flash('success', $messages['created']);
                
                $config = CustomerConfig::getUrlConfig();
                redirect(base_url($config['index']));
            } else {
                $this->customerModel->rollback();
                $messages = CustomerConfig::getErrorMessages();
                flash('error', $messages['create_failed']);
                
                $config = CustomerConfig::getUrlConfig();
                redirect(base_url($config['create']));
            }

        } catch (Exception $e) {
            $this->customerModel->rollback();
            flash('error', $e->getMessage());
            
            $config = CustomerConfig::getUrlConfig();
            redirect(base_url($config['create']));
        }
    }

    /**
     * عرض تفاصيل عميل
     */
    public function show()
    {
        $entity_number = $this->params['id'];
        $data = $this->dataManager->prepareViewData($entity_number, 'view');

        if (!$data) {
            $messages = CustomerConfig::getErrorMessages();
            flash('error', $messages['not_found']);
            
            $config = CustomerConfig::getUrlConfig();
            redirect(base_url($config['index']));
        }

        // إعداد البيانات للـ view (توافق مع Views الحالية)
        $data['customer'] = $data['entity'];
        $data['customerGroups'] = $this->customerGroupModel->getForSelect(current_user()['current_company_id']);
        $data['breadcrumb'] = CustomerBreadcrumbs::getShowBreadcrumb($data['entity']['G_name_ar']);
        unset($data['entity']);

        view('Sales::customers/show', $data);
    }

    /**
     * عرض صفحة تعديل عميل
     */
    public function edit()
    {
        $entity_number = $this->params['id'];
        $data = $this->dataManager->prepareViewData($entity_number, 'edit');

        if (!$data) {
            $messages = CustomerConfig::getErrorMessages();
            flash('error', $messages['not_found']);
            
            $config = CustomerConfig::getUrlConfig();
            redirect(base_url($config['index']));
        }

        // إعداد البيانات للـ view (توافق مع Views الحالية)
        $data['customer'] = $data['entity'];
        $data['customerGroups'] = $this->customerGroupModel->getForSelect(current_user()['current_company_id']);
        $data['breadcrumb'] = CustomerBreadcrumbs::getEditBreadcrumb($data['entity']['G_name_ar']);
        unset($data['entity']);

        view('Sales::customers/edit', $data);
    }

    /**
     * تحديث عميل
     */
    public function update()
    {
        try {
            $entity_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود العميل
            $customer = $this->customerModel->getByNumber($entity_number, $company_id);
            if (!$customer) {
                $messages = CustomerConfig::getErrorMessages();
                flash('error', $messages['not_found']);
                
                $config = CustomerConfig::getUrlConfig();
                redirect(base_url($config['index']));
            }

            // التحقق من صحة البيانات
            $validatedData = $this->validation->validateCustomerData($_POST, $entity_number);

            // فحص التغييرات قبل التحديث
            if (!$this->dataManager->hasDataChanged($customer, $validatedData, $_POST)) {
                $messages = CustomerConfig::getErrorMessages();
                flash('info', $messages['no_changes']);
                
                $config = CustomerConfig::getUrlConfig();
                redirect(base_url(str_replace('{id}', $entity_number, $config['edit'])));
                return;
            }

            $validatedData['updated_by'] = current_user()['UserID'];

            // بدء المعاملة
            $db = $this->customerModel->getDb();
            $db->beginTransaction();

            try {
                // تحديث البيانات الأساسية للعميل
                $result = $this->customerModel->update($entity_number, $validatedData, $company_id);

                if (!$result) {
                    $messages = CustomerConfig::getErrorMessages();
                    throw new Exception($messages['update_failed']);
                }

                // الحصول على entity_id
                $entity_id = $customer['id'];
                $user_id = current_user()['UserID'];

                // تحديث العناوين
                $this->dataManager->updateAddresses($entity_id, $company_id, $_POST, $user_id);

                // تحديث الحسابات البنكية
                $this->dataManager->updateBankAccounts($entity_id, $company_id, $_POST, $user_id);

                // تأكيد المعاملة
                $db->commit();

                $messages = CustomerConfig::getSuccessMessages();
                flash('success', str_replace('{number}', $entity_number, $messages['updated']));
                
                $config = CustomerConfig::getUrlConfig();
                redirect(base_url($config['index']));

            } catch (Exception $e) {
                // إلغاء المعاملة
                $db->rollBack();
                throw $e;
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            
            $config = CustomerConfig::getUrlConfig();
            redirect(base_url(str_replace('{id}', $this->params['id'], $config['edit'])));
        }
    }

    /**
     * حذف عميل
     */
    public function delete()
    {
        try {
            $entity_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود العميل
            $customer = $this->customerModel->getByNumber($entity_number, $company_id);
            if (!$customer) {
                $messages = CustomerConfig::getErrorMessages();
                flash('error', $messages['not_found']);
                
                $config = CustomerConfig::getUrlConfig();
                redirect(base_url($config['index']));
            }

            // حذف العميل
            $result = $this->customerModel->delete($entity_number, $company_id);

            if ($result) {
                $messages = CustomerConfig::getSuccessMessages();
                flash('success', $messages['deleted']);
            } else {
                $messages = CustomerConfig::getErrorMessages();
                flash('error', $messages['delete_failed']);
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
        }

        $config = CustomerConfig::getUrlConfig();
        redirect(base_url($config['index']));
    }

    /**
     * التأكد من وجود مجموعة افتراضية للعملاء
     */
    private function ensureDefaultCustomerGroup()
    {
        $company_id = current_user()['current_company_id'];

        // التحقق من وجود مجموعات للعملاء
        $groups = $this->customerGroupModel->getForSelect($company_id);

        if (empty($groups)) {
            // إنشاء مجموعة افتراضية
            try {
                $defaultGroupData = [
                    'company_id' => $company_id,
                    'name_ar' => 'عملاء عامون',
                    'name_en' => 'General Customers',
                    'created_by' => current_user()['UserID']
                ];

                $this->customerGroupModel->create($defaultGroupData);
            } catch (Exception $e) {
                // تجاهل الخطأ إذا كانت المجموعة موجودة بالفعل
                error_log("Could not create default customer group: " . $e->getMessage());
            }
        }
    }
}
